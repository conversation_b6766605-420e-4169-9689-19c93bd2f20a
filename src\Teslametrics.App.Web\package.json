{"devDependencies": {"typescript": "^5", "esbuild": "^0.23"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "@microsoft/signalr-protocol-msgpack": "^8.0.7", "mitt": "^3.0.1"}, "scripts": {"typecheck": "tsc -p tsconfig.json --noEmit", "build:full": "npm ci && npm run build:esbuild", "build:esbuild": "esbuild \"Components/**/*.razor.ts\" \"Features/**/*.razor.ts\" --bundle --splitting --format=esm --platform=browser --sourcemap --minify --outbase=. --outdir=wwwroot/js/compiled --entry-names=[dir]/[name]-module --chunk-names=chunks/[name]-[hash] --tsconfig=tsconfig.json", "copy:vendor": "node scripts/copy-vendor.mjs"}}