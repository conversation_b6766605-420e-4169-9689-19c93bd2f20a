using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.CameraList;

public partial class CameraListComponent : InteractiveBaseComponent
{
    [Parameter] public List<CamModel> Cameras { get; set; } = [];
    [Parameter] public EventCallback<CamModel> OnCameraAdded { get; set; }
    [Parameter] public EventCallback<CamModel> OnCameraRemoved { get; set; }
    [Parameter] public Func<string, CancellationToken, Task<IEnumerable<CamModel>>>? SearchFunc { get; set; }

    private CamModel? _selectedCamera;

    private async Task<IEnumerable<CamModel>> SearchAsync(string value, CancellationToken token)
    {
        if (SearchFunc == null)
            return Enumerable.Empty<CamModel>();

        return await SearchFunc(value, token);
    }

    private async Task AddCamera()
    {
        if (_selectedCamera == null) return;

        // Проверяем, что камера еще не добавлена
        if (Cameras.Any(c => c.Id == _selectedCamera.Id))
        {
            Snackbar.Add("Эта камера уже добавлена в комнату.", MudBlazor.Severity.Warning);
            return;
        }

        await OnCameraAdded.InvokeAsync(_selectedCamera);
        _selectedCamera = null;
    }

    private async Task RemoveCamera(CamModel camera)
    {
        await OnCameraRemoved.InvokeAsync(camera);
    }
}
