using System;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.PDFExport;

public partial class ExportTemplate
{
    #region [Types]
    public record Filters(DateTime DateFrom, DateTime DateTo, string? CityName, string? BuildingName, string? FloorName, string? RoomName, string? DeviceName, IncidentType? IncidentType, bool? IsResolved);

    public record Incident(Guid Id,
                            string City,
                            string Address,
                            int Floor,
                            string Room,
                            string Device,
                            DateTimeOffset Date,
                            IncidentType IncidentType,
                            bool IsResolved);
    #endregion

    #region [Parameters]
    [Parameter]
    public Filters Filter { get; set; } = null!;

    [Parameter]
    public List<Incident> Incidents { get; set; } = null!;
    #endregion

    private static string GetIncidentIcon(IncidentType type) => type switch
    {
        Teslametrics.Shared.IncidentType.Door => TeslaIcons.Sensors.Door,
        Teslametrics.Shared.IncidentType.Temperature => TeslaIcons.Sensors.Temperature,
        Teslametrics.Shared.IncidentType.Humidity => TeslaIcons.Sensors.Humidity,
        Teslametrics.Shared.IncidentType.Leak => TeslaIcons.Sensors.Leak,
        Teslametrics.Shared.IncidentType.Power => TeslaIcons.Sensors.Power,
        _ => MudBlazor.Icons.Material.Outlined.ReportProblem
    };
}
