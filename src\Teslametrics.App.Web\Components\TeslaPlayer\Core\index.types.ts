import type { IPlayerCore } from "./PlayerCore.types";
import type { IPluginBase } from "../Plugins/PluginBase.types.js";
import type { PlayerCoreOptions } from "./PlayerCore.js";

//import { SignalRFeed } from "./Core/SignalRFeed";
//import MediaPipeline from "./Core/MediaPipeline";
//import { PlayerCore } from "./PlayerCore.types";
//import { MODES } from "./modes";

export type Unsub = () => void;

/**
 * Экземпляр, возвращаемый фабрикой `createPlayer`.
 * Содержит ядро плеера и список подключённых плагинов.
 */
export interface PlayerInstance {
	/** Экземпляр {@link PlayerCore}. */
	core: IPlayerCore;

	/** Активные плагины, созданные на основе {@link PluginBase}. */
	plugins: IPluginBase[];
}

/**
 * Собрать новый плеер.
 * @param pluginList  массив классов-плагинов (`class … extends PluginBase`)
 */
export declare function createPlayer(
	video: HTMLVideoElement,
	opts: PlayerCoreOptions,
	pluginList?: Array<IPluginBase>
): PlayerInstance;

// const players = new Map();

// export function initializePlayer(
// 	videoRef,
// 	cameraId,
// 	mode, // 'live' | 'point' | 'range'
// 	startIso, // string or null
// 	endIso, // string or null
// 	pluginFactories = [] // [ core => new SomePlugin(core), ... ]
// ) {
// 	// 1) Если плеер с этим ID есть — остановить его
// 	if (players.has(cameraId)) {
// 		const old = players.get(cameraId);
// 		old.bus.all.clear();
// 		old.dispose();
// 		players.delete(cameraId);
// 	}

// 	// 2) Собираем конфиг
// 	const modeArgs = {};
// 	if (mode === "point" || mode === "range") {
// 		modeArgs.start = new Date(startIso);
// 	}
// 	if (mode === "range") {
// 		modeArgs.end = new Date(endIso);
// 	}
// 	const cfg = MODES[mode](modeArgs);

// 	// 3) Создаём feed и pipeline
// 	const feed = new SignalRFeed(cameraId, {
// 		wsPath: cfg.wsPath,
// 		autoReconnect: cfg.autoreconnect,
// 	});
// 	const pipeline = new MediaPipeline(videoRef, cfg.mime, {
// 		keep: cfg.keep,
// 	});

// 	// 4) Создаём ядро
// 	const core = new PlayerCore(feed, pipeline, mode);

// 	// 5) Регистрируем плагины
// 	for (const factory of pluginFactories) {
// 		const plugin = factory(core);
// 		core.addPlugin(plugin.name, plugin);
// 	}

// 	players.set(cameraId, core);

// 	// 6) Подписываемся на события для Blazor (оставляем как было)
// 	const callback = statusCallbacks.get(cameraId);
// 	if (callback) {
// 		core.bus.on("state", (s) =>
// 			callback.invokeMethodAsync("OnCameraStatusChanged", s)
// 		);
// 		core.bus.on("error", (e) =>
// 			callback.invokeMethodAsync("OnCameraError", e.message)
// 		);
// 		core.bus.on("timeupdate", (t) =>
// 			callback.invokeMethodAsync("OnTimeUpdate", t.toISOString())
// 		);
// 	}

// 	// 7) Стартуем плеер в нужном режиме
// 	(async () => {
// 		try {
// 			switch (mode) {
// 				case "live":
// 					await core.live();
// 					break;
// 				case "point":
// 					await core.seek(new Date(startIso));
// 					break;
// 				case "range":
// 					await core.setMode("range", {
// 						start: new Date(startIso),
// 						end: new Date(endIso),
// 					});
// 					break;
// 			}
// 		} catch (err) {
// 			console.error("Failed to start player:", err);
// 		}
// 	})();

// 	return { core };
// }

// // Оставляем обёртки pause/resume/live/stop
// export const pauseStream = (id) => players.get(id)?.pause();
// export const resumeStream = (id) => players.get(id)?.resume();
// export const seekToLive = (id) => players.get(id)?.live();
// export const stopStream = (id) => {
// 	const p = players.get(id);
// 	if (p) {
// 		p.bus.all.clear();
// 		p.dispose();
// 		players.delete(id);
// 		statusCallbacks.delete(id);
// 	}
// };
