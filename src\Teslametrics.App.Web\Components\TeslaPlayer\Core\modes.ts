import type { ModeConfig } from "./modes.types.js";

export const MODES: {
	live: () => ModeConfig;
	point: (opts: { start: Date }) => ModeConfig;
	range: (opts: { start: Date; end: Date }) => ModeConfig;
} = {
	live: () => ({
		wsPath: "/videoHub",
		mime: "video/mp2t;codecs=avc1.640028",
		keep: 30,
		autoreconnect: true,
	}),

	point: ({ start }) => ({
		wsPath: "/videoHub?start=" + start.toISOString(),
		mime: "video/mp2t;codecs=avc1.640028",
		keep: 60,
		autoreconnect: true,
	}),

	range: ({ start, end }) => ({
		wsPath: `/videoHub?start=${start.toISOString()}&end=${end.toISOString()}`,
		mime: "video/mp4;codecs=avc1.640028",
		keep: 120,
		autoreconnect: true,
	}),
};
