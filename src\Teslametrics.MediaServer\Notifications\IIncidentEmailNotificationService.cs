using Teslametrics.Core.Domain.Incidents;
using Teslametrics.MediaServer.Orleans.Wirenboard;

namespace Teslametrics.MediaServer.Notifications;

/// <summary>
/// Сервис для отправки email уведомлений об инцидентах
/// </summary>
public interface IIncidentEmailNotificationService
{
    /// <summary>
    /// Отправляет email уведомления пользователям с включенными email уведомлениями о создании инцидента
    /// </summary>
    Task SendIncidentNotificationAsync(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Отправляет email уведомления пользователям с включенными email уведомлениями о разрешении инцидента
    /// </summary>
    Task SendIncidentResolvedNotificationAsync(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo = null, CancellationToken cancellationToken = default);
}
