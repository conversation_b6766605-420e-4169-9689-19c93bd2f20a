{"App": {"Name": "Multimonitor"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Exceptions"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Teslametrics.MediaServer.Notifications": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "Kestrel": {"Endpoints": {"HttpEndpoint": {"Url": "http://0.0.0.0:80"}}}, "ConnectionStrings": {"Default": "Host=;Port=;Database=teslametrics;Username=;Password=", "Kafka": ""}, "Minio": {"Endpoint": "", "AccessKey": "", "SecretKey": ""}, "Orleans": {"Hostname": "", "Endpoint": {}}, "EmailSender": {"Host": "", "Port": 0, "UserName": "", "Password": "", "EnableSsl": false, "FromDisplayName": "", "FromAddress": ""}, "WebApp": {"BaseUrl": ""}, "AllowedHosts": "*"}