import type { Emitter, Handler } from "mitt";
import type {
	VideoChunk,
	ISignalRFeed,
	SignalRError,
} from "./SignalRFeed.types";
import type { Unsub } from "./index.types.js";

import mitt from "mitt";
import * as signalR from "@microsoft/signalr";
import { MessagePackHubProtocol } from "@microsoft/signalr-protocol-msgpack";
import { FeedState } from "./SignalRFeed.types";

const SignalRFeedEvent = {
	Chunk: "chunk",
	State: "state",
	Error: "error",
} as const;

type SignalREvents = {
	[SignalRFeedEvent.Chunk]: VideoChunk;
	[SignalRFeedEvent.State]: FeedState;
	[SignalRFeedEvent.Error]: SignalRError;
};

const enum HubMethods {
	PlayStream = "PlayStreamAsync",
	StopStream = "StopStreamAsync",
	ReceiveVideoSegment = "ReceiveVideoSegment",
}

/**
 * Опции, передаваемые в конструктор {@link SignalRFeed}.
 */
export type SignalRFeedOptions = {
	/**
	 * Адрес, к которому подключается клиент SignalR.
	 * @example "/videoHub"
	 */
	wsPath: string;

	/**
	 * Задержка перед повторным вызовом `start()` при неудаче (в миллисекундах).
	 * Используется для backoff-логики при авто-переподключении.
	 * @default 3000
	 */
	backOffMs?: number;

	/**
	 * Включить автоматическое переподключение.
	 * При `true` вызывается `.withAutomaticReconnect()`.
	 * @defaultValue true
	 */
	autoReconnect?: boolean;
};

/**
 * Тонкий слой поверх @microsoft/signalr:
 *  • открывает HubConnection
 *  • пересылает бинарные сегменты наружу
 *  • держит собственное состояние (connecting / ready / paused / closed)
 *
 * PlayerCore остаётся «агностиком транспорта» – он просто подписывается
 *   feed.onChunk((u8, absDate) => pipeline.push(u8, absDate));
 *   feed.onError(err          => bus.emit('error', err));
 *   feed.onState(newState     => bus.emit('state', newState));
 */
export class SignalRFeed implements ISignalRFeed {
	private readonly _backOffMs: number;
	private _state: FeedState = FeedState.Idle;
	private _hub: signalR.HubConnection;
	private readonly _cameraId: string;
	readonly ev: Emitter<SignalREvents>;

	public get state(): FeedState {
		return this._state;
	}

	constructor(cameraId: string, opts: SignalRFeedOptions) {
		this._cameraId = cameraId;
		this._backOffMs = opts.backOffMs ?? 2_000;
		this.ev = mitt<SignalREvents>();

		let hubBuilder: signalR.HubConnectionBuilder =
			new signalR.HubConnectionBuilder()
				.withUrl(opts.wsPath)
				.configureLogging(signalR.LogLevel.Information)
				.withHubProtocol(new MessagePackHubProtocol());

		if (opts.autoReconnect ?? true) {
			hubBuilder = hubBuilder.withAutomaticReconnect();
		}

		this._onSegmentReceived = this._onSegmentReceived.bind(this);
		this._onReconnecting = this._onReconnecting.bind(this);
		this._onReconnected = this._onReconnected.bind(this);
		this._onClose = this._onClose.bind(this);

		this._hub = hubBuilder.build();
		this._hub.on(HubMethods.ReceiveVideoSegment, this._onSegmentReceived);
		this._hub.onreconnecting(this._onReconnecting);
		this._hub.onreconnected(this._onReconnected);
		this._hub.onclose(this._onClose);
	}

	async start() {
		if (
			this.state === FeedState.Ready ||
			this._hub.state === signalR.HubConnectionState.Connected
		)
			return;

		this._setState(FeedState.Connecting);
		try {
			await this._hub.start();
			await this._hub.invoke(HubMethods.PlayStream, this._cameraId);
			this._setState(FeedState.Ready);
		} catch (e) {
			this._emitError("start()", e);
			setTimeout(() => {
				if (this.state !== FeedState.Stopped) void this.start();
			}, this._backOffMs);
		}
	}

	async seekTo(start: Date): Promise<void> {
		await this._startStream();

		try {
			if (this.state !== FeedState.Ready)
				throw new Error("[SignalRFeed] Not ready");

			await this._hub.invoke(HubMethods.StopStream);
			await this._hub.invoke(
				HubMethods.PlayStream,
				this._cameraId,
				start.toISOString()
			);
			this._setState(FeedState.Ready);
		} catch (err) {
			this._emitError("seekTo", err);
		}
	}

	async seekToLive(): Promise<void> {
		await this._startStream();

		try {
			if (this.state !== FeedState.Ready)
				throw new Error("[SignalRFeed] Not ready");

			await this._invokeSafe(HubMethods.StopStream);
			await this._invokeSafe(HubMethods.PlayStream, this._cameraId);
		} catch (err) {
			this._emitError("seekToLive", err);
			setTimeout(() => {
				if (this.state !== FeedState.Stopped) void this.start();
			}, this._backOffMs);
		}
	}

	pause() {
		if (this.state !== FeedState.Ready) return;
		void this._invokeSafe(HubMethods.StopStream);
		this._setState(FeedState.Paused);
	}

	resume() {
		if (this.state !== FeedState.Paused) return;
		void this._invokeSafe(HubMethods.PlayStream, this._cameraId);
		this._setState(FeedState.Ready);
	}

	async dispose() {
		if (this.state === FeedState.Stopped) return;
		try {
			await this._invokeSafe(HubMethods.StopStream);
		} catch {}
		try {
			await this._hub.stop();
		} catch {}
		this._setState(FeedState.Stopped);
		this.ev.all.clear();
	}

	subscribe<K extends keyof SignalREvents>(
		type: K,
		handler: Handler<SignalREvents[K]>
	): Unsub {
		this.ev.on(type, handler);
		return () => this.ev.off(type, handler);
	}

	/**
	 * Подписка только на поступление медиасегментов.
	 * @param handler `(chunk: { data: Uint8Array; ts?: Date }) => void`
	 */
	public onChunk(handler: Handler<VideoChunk>): Unsub {
		this.ev.on(SignalRFeedEvent.Chunk, handler);
		return () => this.ev.off(SignalRFeedEvent.Chunk, handler);
	}

	/**
	 * Подписка на ошибки транспорта или хаба.
	 * @param handler `(err: Error) => void`
	 */
	onError(handler: Handler<SignalRError>): Unsub {
		this.ev.on(SignalRFeedEvent.Error, handler);
		return () => this.ev.off(SignalRFeedEvent.Error, handler);
	}

	/**
	 * Подписка на изменение состояния подключения.
	 * @param handler `(state: FeedState) => void`
	 */
	onState(handler: Handler<FeedState>): Unsub {
		this.ev.on(SignalRFeedEvent.State, handler);
		return () => this.ev.off(SignalRFeedEvent.State, handler);
	}

	private _setState(s: FeedState): void {
		this._state = s;
		this.ev.emit(SignalRFeedEvent.State, s);
	}

	private async _invokeSafe(method: string, ...args: unknown[]): Promise<void> {
		try {
			await this._hub.invoke(method, ...args);
		} catch (err) {
			this._emitError(method, err);
		}
	}

	private _onSegmentReceived(
		arrayBuffer: ArrayBuffer,
		isoTimestamp?: string
	): void {
		if (!arrayBuffer?.byteLength) return;
		const data = new Uint8Array(arrayBuffer);
		let ts: Date | undefined = undefined;
		if (isoTimestamp) {
			const d = new Date(isoTimestamp);
			if (!isNaN(d.getTime())) ts = d;
		}
		this.ev.emit(SignalRFeedEvent.Chunk, { data, ts } as VideoChunk);
	}

	private async _startStream(): Promise<void> {
		// Если уже готовы — сразу выходим
		if (
			this.state === FeedState.Ready ||
			this._hub.state === signalR.HubConnectionState.Connected
		) {
			return;
		}

		this._setState(FeedState.Connecting);

		// Итерируем до тех пор, пока не подключимся или не остановим фид
		while (this.state !== FeedState.Stopped) {
			try {
				await this._hub.start(); // бросит, если не смог
				this._setState(FeedState.Ready);
				return; // вышли, как только подключились
			} catch (e) {
				this._emitError("_startStream()", e);
				// ждём заданный бэкофф, прежде чем пробовать снова
				await new Promise((res) => setTimeout(res, this._backOffMs));
				// и после таймаута цикл повторится
			}
		}

		// Если сюда дошли, значит state стал Stopped — просто вернёмся
	}

	private _onReconnecting(err: SignalRError | undefined): void {
		this._setState(FeedState.Connecting);
		if (err) this.ev.emit(SignalRFeedEvent.Error, err);
	}

	private _onReconnected(): void {
		this._setState(FeedState.Ready);
		// сервер не знает, что надо возобновить поток – просим заново
		void this._invokeSafe(HubMethods.PlayStream, this._cameraId);
	}

	private _onClose(err: SignalRError | undefined): void {
		this._setState(FeedState.Stopped);
		if (err) this.ev.emit(SignalRFeedEvent.Error, err);
	}

	private _emitError(context: string, err: unknown) {
		const error =
			err instanceof Error
				? err
				: new Error(`[SignalRFeed] ${context}: ${String(err)}`);
		this.ev.emit(SignalRFeedEvent.Error, error);
	}
}
