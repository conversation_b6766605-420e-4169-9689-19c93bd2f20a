{"App": {"Name": "Teslametrics"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Exceptions"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "Kestrel": {"Endpoints": {"HttpEndpoint": {"Url": "http://0.0.0.0:80"}}}, "ConnectionStrings": {"Default": "Host=;Port=;Database=teslametrics;Username=;Password=", "Kafka": ""}, "Minio": {"Endpoint": "", "AccessKey": "", "SecretKey": ""}, "Orleans": {}, "Camera": {"CleanupIntervalMinutes": 1}, "AllowedHosts": "*", "YandexMaps": {"ApiKey": "ef49d312-7309-407a-832a-160a51820c89"}}