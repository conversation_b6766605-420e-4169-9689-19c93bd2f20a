﻿<MudDialog Visible="IsVisible"
           VisibleChanged="IsVisibleChanged"
           ActionsClass="mx-2"
           Class="two_fa_setup_instruction_dialog br_12"
           ContentClass="my-0 pt-5"
           Options="_dialogOptions">
    <DialogContent>
        <MudStack Row=true
                  Justify="Justify.SpaceBetween"
                  AlignItems="AlignItems.Center">
            <MudText Typo="@(_userDeviceService.IsMobile? Typo.h4: Typo.subtitle1)"
                     Class="h_fit_content m_center title">
                Как работает двухфакторная аутентификация?
            </MudText>
            <MudIconButton OnClick="CancelAsync"
                           Icon="@Icons.Material.Outlined.Close"
                           Class="close_button" />
        </MudStack>
        <MudSwipeArea OnSwipeEnd="HandleSwipeEnd">
            <div data-step="@_index"
                 class="grid_container">
                <MudText Typo="@(_userDeviceService.IsMobile? Typo.h4: Typo.subtitle1)"
                         Class="h_fit_content m_center steps">
                    Шаг @(_index + 1)
                </MudText>
                <MudStepper @bind-ActiveIndex="_index"
                            CompletedStepColor="Color.Success"
                            CurrentStepColor="Color.Primary"
                            NavClass="navigation pr-4"
                            StepClass="pa-0 d-flex flex-column gap-2 justify-center">
                    <LabelTemplate>
                        <div class="line"></div>
                    </LabelTemplate>
                    <TitleTemplate>@*This empty template prevents rendering the title*@</TitleTemplate>
                    <ConnectorTemplate Context="step">
                        @*This empty template prevents rendering the connection line*@
                    </ConnectorTemplate>
                    <ChildContent>
                        <MudStep Title="Verify passenger data">
                            <MudText Typo="@(_userDeviceService.IsMobile? Typo.subtitle2: Typo.subtitle1)">
                                Скачайте и установите приложение для двухфакторной аутентификации.
                            </MudText>
                            <MudText Typo="Typo.body1"
                                     Class="text_input_tile">
                                Google Authenticator, Microsoft Authenticator, Яндекс.Ключ, FreeOTP или Twilio Authy.
                            </MudText>
                        </MudStep>
                        <MudStep>
                            <MudText Typo="Typo.subtitle1">Откройте приложение и отсканируйте QR-код на экране.</MudText>
                        </MudStep>
                        <MudStep>
                            <MudText Typo="Typo.subtitle1">Введите сгенерированный код для подтверждения.</MudText>
                        </MudStep>
                    </ChildContent>
                    <ActionContent Context="stepper">
                        @if (_index < 2)
                        {
                            <MudButton OnClick="@(() => stepper.NextStepAsync())"
                                       Variant="Variant.Filled"
                                       Class="next_button"
                                       Color="Color.Primary">Дальше</MudButton>
                        }
                        @if (_index == 2)
                        {
                            <MudButton OnClick="CancelAsync"
                                       Variant="Variant.Filled"
                                       Color="Color.Primary">Закрыть</MudButton>
                        }
                    </ActionContent>
                </MudStepper>
                <div class="phone_images">
                    <img src="/img/phone_apps_mobile.svg"
                         class="phone_apps_mobile"
                         loading="lazy">
                    <img src="/img/phone_qr_code_mobile.svg"
                         class="phone_qr_code_mobile"
                         loading="lazy">
                    <img src="/img/phone_2fa_input_mobile.svg"
                         class="phone_2fa_input_mobile"
                         loading="lazy">

                    <img src="/img/phone_apps.svg"
                         class="phone_apps"
                         loading="lazy">
                    <img src="/img/phone_qr_code.svg"
                         class="phone_qr_code"
                         loading="lazy">
                    <img src="/img/phone_2fa_input.svg"
                         class="phone_2fa_input"
                         loading="lazy">
                </div>
            </div>
        </MudSwipeArea>
    </DialogContent>
</MudDialog>
<style>
    .two_fa_setup_instruction_dialog {
        min-height: 380px;
    }

    .two_fa_setup_instruction_dialog .mud-dialog-content {
        overflow: hidden;
        display: grid;
        grid-template-rows: auto 1fr;
    }

    .two_fa_setup_instruction_dialog .mud-dialog-content .phone_images {
        height: -webkit-fill-available;
        align-self: end;
        transition: 0.2s;
        display: grid;
        min-width: 194px;
        justify-content: center;
        align-items: end;
        grid-area: imgs;
    }

    .two_fa_setup_instruction_dialog .mud-stepper {
        display: grid;
        grid-template:
            "stepper"
            "content"
            "actions";
        grid-template-rows: auto 1fr auto;
        grid-template-rows: auto 1fr auto;
    }

    .two_fa_setup_instruction_dialog .mud-tabs-tabbar .mud-tabs-tabbar-inner {
        min-height: fit-content;
    }

    .two_fa_setup_instruction_dialog .d-inline-block {
        width: -webkit-fill-available !important;
    }

    .two_fa_setup_instruction_dialog .steps {
        margin-top: 32px;
        margin-bottom: 8px;
    }

    .two_fa_setup_instruction_dialog .navigation {
        gap: 2px;
        grid-area: stepper;
    }

    .two_fa_setup_instruction_dialog .navigation button {
        padding: 0 !important;
        height: 2px;
        border-radius: 2px;
        width: -webkit-fill-available;
        background: var(--color-stroke-light);
        transition: 0.2s;
    }

    .two_fa_setup_instruction_dialog .navigation button.active {
        background: var(--color-primary-800);
    }

    .two_fa_setup_instruction_dialog .navigation .mud-stepper-nav-connector {
        margin-top: 0px !important;
        margin: 0;
    }

    .two_fa_setup_instruction_dialog .grid_container {
        display: grid !important;
        grid-template:
            "content imgs"
            "stepper imgs";
        grid-template-rows: auto 1fr;
        height: -webkit-fill-available;
    }

    .two_fa_setup_instruction_dialog .mud-card-actions {
        padding-left: 0px;
        padding-bottom: 20px;
        grid-area: actions;
    }

    .two_fa_setup_instruction_dialog .phone_apps,
    .two_fa_setup_instruction_dialog .phone_qr_code,
    .two_fa_setup_instruction_dialog .phone_2fa_input,
    .two_fa_setup_instruction_dialog .phone_apps_mobile,
    .two_fa_setup_instruction_dialog .phone_qr_code_mobile,
    .two_fa_setup_instruction_dialog .phone_2fa_input_mobile {
        display: none;
        grid-area: 1 / 1;
        align-items: center;
    }

    .two_fa_setup_instruction_dialog div[data-step="0"] .phone_apps,
    .two_fa_setup_instruction_dialog div[data-step="1"] .phone_qr_code,
    .two_fa_setup_instruction_dialog div[data-step="2"] .phone_2fa_input {
        display: block;
    }


    .two_fa_setup_instruction_dialog .close_button {
        position: absolute;
        right: 6px;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog {
        border-radius: 0 !important;
    }

    .mud-dialog-fullscreen .title {
        align-self: end;
        margin-bottom: 44px;
    }

    .mud-dialog-fullscreen .close_button {
        display: none;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .steps {
        margin-top: 20px;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .mud-dialog-content {
        grid-template-rows: 30% 1fr;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .mud-dialog-content .phone_images {
        min-height: 350px;
    }

    .mud-dialog-fullscreen .grid_container {
        grid-template:
            "imgs"
            "content"
            "stepper";
        grid-template-rows: auto auto 1fr;
    }

    .mud-dialog-fullscreen .m_center {
        text-align: center;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .mud-stepper {
        grid-template:
            "content"
            "actions"
            "stepper";
        grid-template-rows: min-content 1fr auto;
        padding-bottom: 32px;
        text-align: center;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .mud-stepper .mud-stepper-content {
        align-self: start;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .phone_apps,
    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .phone_qr_code,
    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .phone_2fa_input {
        display: none !important;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog div[data-step="0"] .phone_apps_mobile,
    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog div[data-step="1"] .phone_qr_code_mobile,
    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog div[data-step="2"] .phone_2fa_input_mobile {
        display: block;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .mud-card-actions {
        padding-top: 32px;
        justify-content: center;
        align-items: start;
    }

    .mud-dialog-fullscreen.two_fa_setup_instruction_dialog .next_button {
        visibility: hidden;
    }
</style>