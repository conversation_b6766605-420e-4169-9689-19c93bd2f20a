{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Graylog", "Serilog.Exceptions"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "Graylog", "Args": {"hostnameOrAddress": "**************", "port": "12202", "transportType": "Udp"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "Kestrel": {"Endpoints": {"HttpEndpoint": {"Url": "http://0.0.0.0:80"}}}, "ConnectionStrings": {"Default": "Server=localhost;Port=3306;Database=teslametrics.app;Uid=root;Pwd=****************;Allow User Variables=True;"}, "Orleans": {"Hostname": "media-server", "Endpoint": {}}, "EmailSender": {"Host": "", "Port": 0, "UserName": "", "Password": "", "EnableSsl": false, "FromDisplayName": "", "FromAddress": ""}, "WebApp": {"BaseUrl": ""}, "AllowedHosts": "*"}