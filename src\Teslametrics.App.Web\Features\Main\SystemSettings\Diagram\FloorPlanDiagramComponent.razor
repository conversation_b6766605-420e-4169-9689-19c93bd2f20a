@using Blazor.Diagrams.Components.Widgets
@using Blazor.Diagrams.Components
@using Blazor.Diagrams
@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
@inherits InteractiveBaseComponent
<div class="pa-4 overflow-hidden mud-height-full">
    <MudStack Row="true">
        <MudText Typo="Typo.h6">План этажа:</MudText>
        <MudSpacer />
        @if (Floor?.Plan is null)
        {
            <MudFileUpload T="IBrowserFile"
                           FilesChanged="OnFileChanged"
                           Accept=".jpg,.jpeg,.png,.gif,.svg">
                <ActivatorContent>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.CloudUpload">
                        Загрузить изображение
                    </MudButton>
                </ActivatorContent>
            </MudFileUpload>
        }
        else
        {
            <MudButton OnClick="DeleteImage"
                       Color="Color.Error"
                       StartIcon="@Icons.Material.Filled.Delete">
                Удалить изображение
            </MudButton>
        }
    </MudStack>

    <div class="mt-4 plan_container">
        @if (SelectedRoom is not null)
        {
            <div class="diagram_buttons">
                @if (IsEditInProgress)
                {
                    <MudText Typo="Typo.subtitle2">
                        Нажмите на карте, чтобы установить точку зоны. Нажмите на точку дважды для её удаления.
                    </MudText>
                    <MudSpacer />
                    <MudButton OnClick="EndRoomZoneEdit">Завершить редактирование</MudButton>
                }
                else
                {
                    <MudSpacer />
                    <MudButton OnClick="StartRoomZoneEdit">Изменить зону комнаты</MudButton>
                }
            </div>
        }

        @if (Diagram is not null)
        {
            <CascadingValue Value="Diagram">
                <DiagramCanvas>
                    <Widgets>
                        <SelectionBoxWidget />
                        <GridWidget Size="15"
                                    Mode="GridMode.Line"
                                    BackgroundColor="transparent" />
                    </Widgets>
                </DiagramCanvas>
            </CascadingValue>
        }
        else
        {
            <div class="d-flex justify-center align-center"
                 style="height: 400px;">
                <MudText Typo="Typo.body1"
                         Color="Color.Secondary">
                    Диаграмма не инициализирована
                </MudText>
            </div>
        }
    </div>

    <style>
        div.grid {
            background-image: linear-gradient(var(--mud-palette-divider) 1px, transparent 1px), linear-gradient(90deg, var(--mud-palette-divider) 1px, transparent 1px) !important;
        }
    </style>
</div>
