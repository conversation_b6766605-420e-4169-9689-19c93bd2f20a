// Converted from MediaPipeline.js
import type { Emitter } from "mitt";
import type { IMediaPipeline } from "./MediaPipeline.types.js";

import mitt from "mitt";

type PendingChunk = { data: Uint8Array; ts?: Date };

/**
 * Параметры настройки MediaPipeline.
 */
export type MediaPipelineOptions = {
	/**
	 * Время хранения данных в секундах. Используется для trim-очистки буфера.
	 * @default 60
	 */
	keep?: number;

	/**
	 * Интервал времени (в миллисекундах) между событиями `timeupdate`, эмитируемыми MediaPipeline.
	 * @default 500
	 */
	timeupdateInterval?: number; // TODO. Вероятно это будет не в opts, а будет прокси-реакцией от video и время будет передаваться во время события.

	/**
	 * Максимальный размер буфера в секундах.
	 * При превышении будет выполнена проактивная очистка.
	 * @default 150
	 */
	maxBufferSize?: number;

	/**
	 * Пороговое значение размера буфера, при достижении которого включается регулярная очистка.
	 * @default 120
	 */
	bufferCleanupThreshold?: number;

	/**
	 * Размер агрессивной очистки буфера в секундах.
	 * Применяется при возникновении QuotaExceededError.
	 * @default 30
	 */
	aggressiveCleanupSize?: number;
};

export class MediaPipeline implements IMediaPipeline {
	private readonly video: HTMLVideoElement;
	private readonly mime: string;
	private readonly keep: number;
	private readonly timeupdateInterval: number;

	private _mediaSource = new MediaSource();
	private _sourceBuffer: SourceBuffer | null = null;
	private _pending: PendingChunk[] = [];
	private _trimTimer: ReturnType<typeof setInterval> | null = null;
	private _timeTimer: ReturnType<typeof setInterval> | null = null;
	private _healthMonitorTimer: ReturnType<typeof setInterval> | null = null;
	private _bufferMonitorTimer: ReturnType<typeof setInterval> | null = null;

	private _t0Media = 0;
	private _t0Abs: Date | null = null;
	private _segmentTimestamps = new Map<number, Date>();

	private _maxBufferSize: number;

	private _maxPendingSize = 0;
	private _autoJumpDone = false;

	readonly ev: Emitter<Record<string, unknown>> = mitt();

	public constructor(
		video: HTMLVideoElement,
		mime: string,
		opts: Partial<MediaPipelineOptions> = {}
	) {
		this.video = video;
		this.mime = mime;
		this.keep = opts.keep ?? 60;
		this.timeupdateInterval = opts.timeupdateInterval ?? 500;

		this._maxBufferSize = opts.maxBufferSize ?? 150;

		this._initMediaSource();
		this._startHealthMonitoring();
	}

	get bufferStart(): number {
		return this._ranges()?.start(0) ?? 0;
	}

	get bufferEnd(): number {
		return this._ranges()?.end(0) ?? 0;
	}

	get bufferSize(): number {
		return this.bufferEnd - this.bufferStart;
	}

	get currentAbsTime(): Date | null {
		if (!this._t0Abs) return null;
		const current = this.video.currentTime;
		return (
			this._interpolateAbsoluteTime(current) ??
			new Date(this._t0Abs.getTime() + (current - this._t0Media) * 1000)
		);
	}

	push(chunk: Uint8Array, absTime?: Date): void {
		if (this._sourceBuffer?.updating) {
			this._pending.push({ data: chunk, ts: absTime } as PendingChunk);
		} else {
			this._appendChunk({ data: chunk, ts: absTime } as PendingChunk);
		}
	}

	seekAbs(time: Date): void {
		if (!this._t0Abs) return;
		const delta = (time.getTime() - this._t0Abs.getTime()) / 1000;
		const t = this._t0Media + delta;
		const adjusted = Math.max(t, this.bufferStart + 0.1);
		this.video.currentTime = adjusted;
		this._emitTimeupdate();
	}

	seekLive(): void {
		const live = this.bufferEnd - 0.5;
		if (live > 0) this.video.currentTime = live;
	}

	clearBuffer(): void {
		if (
			this._sourceBuffer &&
			!this._sourceBuffer.updating &&
			this.bufferEnd > this.bufferStart
		) {
			try {
				this._sourceBuffer.remove(this.bufferStart, this.bufferEnd);
				this._segmentTimestamps.clear();
				this._t0Abs = null;
				this._t0Media = 0;
				this._autoJumpDone = false;
			} catch {}
		}
	}

	dispose(): void {
		this.ev.all.clear();
		if (this._trimTimer) clearInterval(this._trimTimer);
		if (this._timeTimer) clearInterval(this._timeTimer);
		if (this._healthMonitorTimer) clearInterval(this._healthMonitorTimer);
		if (this._bufferMonitorTimer) clearInterval(this._bufferMonitorTimer);
		try {
			this._sourceBuffer?.abort();
		} catch {}
		try {
			if (this._mediaSource.readyState === "open")
				this._mediaSource.endOfStream();
		} catch {}
	}

	private _initMediaSource(): void {
		this.video.src = URL.createObjectURL(this._mediaSource);
		this._mediaSource.addEventListener("sourceopen", () => {
			if (!MediaSource.isTypeSupported(this.mime)) {
				this.ev.emit("error", new Error(`MIME not supported: ${this.mime}`));
				return;
			}
			try {
				this._sourceBuffer = this._mediaSource.addSourceBuffer(this.mime);
			} catch (e) {
				this.ev.emit("error", e);
				return;
			}
			this._sourceBuffer.mode = "segments";
			this._sourceBuffer.addEventListener("error", (e) =>
				this.ev.emit("error", e)
			);
			this._sourceBuffer.addEventListener("updateend", () => {
				const next = this._pending.shift();
				if (next) this._appendChunk(next);
				if (!this._autoJumpDone && this.bufferStart > 0) {
					const target = this.bufferEnd - 0.3;
					this.video.currentTime = target;
					this._autoJumpDone = true;
					this.video.muted = true;
					this.video.play().catch(() => {});
				}
				this._scheduleTrim();
			});
			this._trimTimer = setInterval(() => this._trimBuffer(), 3000);
			this._timeTimer = setInterval(
				() => this._emitTimeupdate(),
				this.timeupdateInterval
			);
			this._bufferMonitorTimer = setInterval(
				() => this._monitorBufferSize(),
				2000
			);
		});
	}

	private _appendChunk(packet: PendingChunk): void {
		if (!packet.data || !(packet.data instanceof Uint8Array)) return;
		const chunk = packet.data;
		const absTime = packet.ts instanceof Date ? packet.ts : undefined;

		if (!this._sourceBuffer || this._sourceBuffer.updating) {
			this._pending.push(packet);
			return;
		}

		try {
			this._sourceBuffer.appendBuffer(chunk);
		} catch (e) {
			this.ev.emit("error", e);
			return;
		}

		if (!this._t0Abs && absTime) {
			this._t0Abs = new Date(absTime);
			this._t0Media = this._ranges()?.start(0) ?? 0;
		}

		if (absTime) {
			var ranges: TimeRanges | null = this._ranges();
			if (ranges) {
				const mediaTime = ranges.end(ranges.length - 1) ?? this._t0Media;
				this._segmentTimestamps.set(mediaTime, new Date(absTime));
				if (this._segmentTimestamps.size > 50) {
					const [first] = this._segmentTimestamps.keys();
					this._segmentTimestamps.delete(first!);
				}
			}
		}

		this.ev.emit("buffer", { start: this.bufferStart, end: this.bufferEnd });
	}

	private _ranges(): TimeRanges | null {
		const sb = this._sourceBuffer;
		return sb?.buffered?.length ? sb.buffered : null;
	}

	private _scheduleTrim(): void {
		queueMicrotask(() => this._trimBuffer());
	}

	private _trimBuffer(): void {
		const excess = this.bufferEnd - this.bufferStart - this.keep;
		if (excess <= 0 || this._sourceBuffer?.updating) return;
		const candidate = Math.min(
			this.video.currentTime - 5,
			this.bufferStart + excess + 2
		);
		if (candidate > this.bufferStart + 0.1) {
			try {
				this._sourceBuffer?.remove(this.bufferStart, candidate);
			} catch {}
		}
	}

	private _monitorBufferSize(): void {
		if (
			this.bufferSize > this._maxBufferSize &&
			!this._sourceBuffer?.updating
		) {
			this._trimBuffer();
		}
	}

	private _emitTimeupdate(): void {
		const t = this.currentAbsTime;
		if (t && !isNaN(t.getTime())) {
			this.ev.emit("timeupdate", t);
		}
	}

	private _startHealthMonitoring(): void {
		this._healthMonitorTimer = setInterval(() => {
			this._maxPendingSize = Math.max(
				this._maxPendingSize,
				this._pending.length
			);
		}, 10000);
	}

	private _interpolateAbsoluteTime(mediaTime: number): Date | null {
		if (this._segmentTimestamps.size < 2) return null;
		let prevTime: number | null = null,
			nextTime: number | null = null;
		let prevAbs: Date | null = null,
			nextAbs: Date | null = null;
		for (const [t, d] of this._segmentTimestamps) {
			if (t <= mediaTime && (prevTime === null || t > prevTime)) {
				prevTime = t;
				prevAbs = d;
			}
			if (t >= mediaTime && (nextTime === null || t < nextTime)) {
				nextTime = t;
				nextAbs = d;
			}
		}
		if (prevTime === mediaTime && prevAbs) return new Date(prevAbs);
		if (
			prevTime !== null &&
			nextTime !== null &&
			prevAbs &&
			nextAbs &&
			prevTime !== nextTime
		) {
			const ratio = (mediaTime - prevTime) / (nextTime - prevTime);
			const diff = nextAbs.getTime() - prevAbs.getTime();
			return new Date(prevAbs.getTime() + diff * ratio);
		}
		if (prevTime !== null && prevAbs)
			return new Date(prevAbs.getTime() + (mediaTime - prevTime) * 1000);
		return null;
	}
}

export default MediaPipeline;
