using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Navigation;

public partial class BreadcrumbNavigationComponent
{
    [Parameter] public CityModel? SelectedCity { get; set; }
    [Parameter] public BuildingModel? SelectedBuilding { get; set; }
    [Parameter] public FloorModel? SelectedFloor { get; set; }
    [Parameter] public RoomModel? SelectedRoom { get; set; }
    [Parameter] public FreezerModel? SelectedFreezer { get; set; }

    [Parameter] public EventCallback<CityModel?> OnCitySelected { get; set; }
    [Parameter] public EventCallback<BuildingModel?> OnBuildingSelected { get; set; }
    [Parameter] public EventCallback<FloorModel?> OnFloorSelected { get; set; }
    [Parameter] public EventCallback<RoomModel?> OnRoomSelected { get; set; }
    [Parameter] public EventCallback<FreezerModel?> OnFreezerSelected { get; set; }
}
