using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Teslametrics.MediaServer.Notifications;

/// <summary>
/// Модуль для регистрации всех сервисов уведомлений
/// </summary>
public static class NotificationModule
{
    /// <summary>
    /// Кешированный результат проверки конфигурации email
    /// </summary>
    private static bool? _isEmailConfigured;

    /// <summary>
    /// Регистрирует все сервисы уведомлений в DI контейнере
    /// </summary>
    public static void Install(IServiceCollection services, IConfiguration configuration)
    {
        // Конфигурация email
        services.Configure<EmailSenderSettings>(
            configuration.GetSection(EmailSenderSettings.SectionName));

        // Конфигурация веб-приложения
        services.Configure<WebAppSettings>(
            configuration.GetSection(WebAppSettings.SectionName));

        // Проверяем и кешируем конфигурацию email один раз при запуске
        _isEmailConfigured = CheckEmailConfiguration(configuration);

        // Email сервисы - используем MailKit для лучшей поддержки различных SMTP конфигураций
        services.AddTransient<IEmailSender, MailKitEmailSender>();

        // Сервисы уведомлений об инцидентах
        services.AddTransient<IIncidentEmailNotificationService, IncidentEmailNotificationService>();
    }

    /// <summary>
    /// Возвращает кешированный результат проверки конфигурации email
    /// </summary>
    public static bool IsEmailSenderConfigured => _isEmailConfigured ?? false;

    /// <summary>
    /// Проверяет конфигурацию email отправителя
    /// </summary>
    private static bool CheckEmailConfiguration(IConfiguration configuration)
    {
        var emailSettings = configuration.GetSection(EmailSenderSettings.SectionName);
        var host = emailSettings["Host"];
        var fromAddress = emailSettings["FromAddress"];

        return !string.IsNullOrEmpty(host) && !string.IsNullOrEmpty(fromAddress);
    }
}
