import type { Unsub } from "./index.types.js";
import type { <PERSON><PERSON>er<PERSON><PERSON><PERSON>, BufferSize } from "./MediaPipeline.types.js";
import type { ViewMode } from "./modes.types.js";
import type {
	FeedState,
	SignalRError,
	VideoChunk,
} from "./SignalRFeed.types.js";
import type { Emitter, Handler } from "mitt";
import type { IPluginBase } from "../Plugins/PluginBase.types.js";

/** Параметры, нужные при выборе режима */
export interface ModeArgs {
	/**
	 * Начальная точка воспроизведения для режимов `'point'` и `'range'`.
	 * Принимает `Date` или ISO-строку.
	 */
	start?: Date | string | null;

	/**
	 * Конечная точка диапазона воспроизведения (используется только в режиме `'range'`).
	 * Принимает `Date` или ISO-строку.
	 */
	end?: Date | string | null;
}

export enum PlayerCoreEvent {
	Chunk = "chunk",
	State = "state",
	Error = "error",
	HealthWarning = "health-warning",
	TimeUpdate = "timeupdate",
	BufferRange = "buffer",
	BufferSize = "buffer-size",
	SeekAbs = "seek-abs",
	SeekLive = "seek-live",
	ModeChanged = "mode-changed",
	Dispose = "dispose",
	PluginEvent = "plugin",
	Diagnostics = "diagnostics",
	Paused = "paused",
	Resumed = "resumed",
	SeekRange = "seek-range",
}

export type HealthWarning = {
	type: string;
	report: unknown;
};

export type PlayerCoreBusEvents = {
	[PlayerCoreEvent.Chunk]: VideoChunk;
	[PlayerCoreEvent.State]: FeedState;
	[PlayerCoreEvent.Error]: SignalRError;
	[PlayerCoreEvent.HealthWarning]: HealthWarning;
	[PlayerCoreEvent.TimeUpdate]: Date;
	[PlayerCoreEvent.BufferRange]: BufferRange;
	[PlayerCoreEvent.BufferSize]: BufferSize;
	[PlayerCoreEvent.SeekAbs]: Date;
	[PlayerCoreEvent.SeekRange]: {
		start?: Date;
		end?: Date;
	};
	[PlayerCoreEvent.Paused]: void;
	[PlayerCoreEvent.Resumed]: void;
	[PlayerCoreEvent.SeekLive]: void;
	[PlayerCoreEvent.ModeChanged]: ViewMode;
	[PlayerCoreEvent.Dispose]: void;
	[PlayerCoreEvent.PluginEvent]: { name: string; event: string; data: unknown };
	[PlayerCoreEvent.Diagnostics]: Record<string, unknown>;
};

/**
 * Ядро видеоплеера.
 *
 * Создаёт связку:
 * - {@link SignalRFeed} — получение медиасегментов по SignalR;
 * - {@link MediaPipeline} — буферизация и воспроизведение через MSE.
 *
 * Все события проксируются в событийную шину {@link IPlayerCore.bus}.
 */
export interface IPlayerCore {
	/**
	 * Активный режим воспроизведения:
	 * - `'live'`  — реальное время,
	 * - `'point'` — начало с точки {@link PlayerCoreOptions.start},
	 * - `'range'` — диапазон {@link PlayerCoreOptions.start} → {@link PlayerCoreOptions.end}.
	 */
	mode: ViewMode;

	/**
	 * Событийная шина (mitt).
	 *
	 * Часто используемые события:
	 * - `'timeupdate'` → `Date` — абсолютное время воспроизведения;
	 * - `'state'` → `string`   — `'playing' | 'paused' | 'stopped' …`;
	 * - `'error'` → `Error`    — ошибки транспорта или MSE.
	 */
	readonly bus: Emitter<PlayerCoreBusEvents>;

	/**
	 * Приостанавливает получение потока и воспроизведение.
	 */
	pause(): void;

	/**
	 * Возобновляет получение потока и воспроизведение.
	 * Если соединение ещё не установлено, вызывает `feed.start()`.
	 */
	resume(): void;

	/**
	 * Перемещается на live-край потока (последний доступный сегмент).
	 * Если плеер находится в режиме `'live'`, ничего не делает.
	 * Если плеер находится в режиме `'point'` или `'range'`, меняет режим на `'live'`.
	 * @returns Promise<void>
	 * @throws Error, если плеер был уничтожен.
	 * @throws Error, если плеер ещё не инициализирован.
	 * @throws Error, если произошла какая-либо другая ошибка.
	 */
	seekLive(): void;

	/**
	 * Выполняет seek к заданному абсолютному времени.
	 * Если плеер находится в режиме `'live'`, меняет режим на `'point'`.
	 * @param time Абсолютное время (wall-clock), к которому нужно перемотать.
	 * @returns Promise<void>
	 * @throws Error, если время находится за пределами доступного диапазона.
	 * @throws Error, если произошла ошибка при перемотке.
	 * @throws Error, если плеер был уничтожен.
	 * @throws Error, если плеер ещё не инициализирован.
	 * @throws Error, если передано несуществующее время.
	 * @throws Error, если произошла какая-либо другая ошибка.
	 */
	seekAbs(time: Date): void;

	/**
	 * Переключает режим воспроизведения.
	 * @param newMode  Новый режим ('live' | 'point' | 'range')
	 * @param args     Параметры режима (start / end)
	 */
	setMode(newMode: ViewMode, args?: ModeArgs): Promise<void>;

	subscribe<T extends PlayerCoreEvent>(
		event: T,
		handler: Handler<PlayerCoreBusEvents[T]>
	): Unsub;

	addPlugin(plugin: IPluginBase): void;

	removePlugin(name: string): void;

	/**
	 * Полностью освобождает ресурсы:
	 * - останавливает SignalR-соединение;
	 * - закрывает MediaPipeline;
	 * - очищает все подписки на шину `bus`.
	 */
	dispose(): void;
}
