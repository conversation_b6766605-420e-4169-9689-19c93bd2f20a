using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.Export;

public static class GetFilterNamesUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? DeviceId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string? CityName { get; init; }
        public string? BuildingName { get; init; }
        public string? FloorName { get; init; }
        public string? RoomName { get; init; }
        public string? DeviceName { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(string? cityName = null, string? buildingName = null, string? floorName = null, string? roomName = null, string? deviceName = null)
        {
            CityName = cityName;
            BuildingName = buildingName;
            FloorName = floorName;
            RoomName = roomName;
            DeviceName = deviceName;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.DateTo).GreaterThan(q => q.DateFrom);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            Teslametrics.Shared.City? city = null;
            Teslametrics.Shared.Building? building = null;
            Teslametrics.Shared.Floor? floor = null;
            Teslametrics.Shared.Room? room = null;
            Teslametrics.Shared.Fridge? device = null;

            if (request.CityId is not null)
            {
                var planTemplate = SqlQueryBuilder.Create()
                    .Select(Db.Plans.Props.Page)
                    .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

                var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(planTemplate.RawSql);

                if (string.IsNullOrEmpty(pageJson))
                {
                    return new Response(Result.PlanNotFound);
                }

                var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

                city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

                if (request.BuildingId is not null && city is not null)
                {
                    building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

                    if (request.FloorId is not null && building is not null)
                    {
                        floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

                        if (request.RoomId is not null && floor is not null)
                        {
                            room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

                            if (request.DeviceId is not null && room is not null)
                            {
                                device = room.Fridges.FirstOrDefault(f => f.Id == request.DeviceId);
                            }
                        }
                    }
                }
            }

            return new Response(
                city?.Name,
                building?.Address,
                floor?.Number.ToString(),
                room?.Name,
                device?.Name
            );
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid Id, string City, string Building, int Floor, string Room, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt);
}
