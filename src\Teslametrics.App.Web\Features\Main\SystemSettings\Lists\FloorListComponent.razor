@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
<MudList T="FloorModel"
         SelectedValue="SelectedFloor"
         SelectedValueChanged="SelectedValueChanged">
    <MudListSubheader Class="d-flex">
        Список этажей в здании
        <MudSpacer />
        <MudButton StartIcon="@Icons.Material.Outlined.Add"
                   OnClick="@(() => AddFloor())"
                   Disabled="string.IsNullOrWhiteSpace(Building?.Address)"
                   Color="Color.Primary">
            Добавить этаж
        </MudButton>
    </MudListSubheader>

    @foreach (var item in Floors)
    {
        <MudListItem Value="@item"
                     Icon="@Icons.Material.Filled.Reorder"
                     @key="@item">
            <ChildContent>
                <div class="d-flex flex-row align-center gap-3">
                    <div class="d-flex flex-column px-6 py-2">
                        <MudText Typo="Typo.body1">
                            Этаж: @item.Number.ToString()
                        </MudText>
                    </div>
                    <MudSpacer />
                    <MudTooltip Text="Удалить этаж">
                        <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                       Color="Color.Warning"
                                       OnClick="@(() => RemoveFloor(item))" />
                    </MudTooltip>
                </div>
            </ChildContent>
        </MudListItem>
    }

    @if (Floors.Count == 0)
    {
        <div class="pa-4">
            <NoItemsFoundComponent HasItems="false" />
        </div>
    }
</MudList>
