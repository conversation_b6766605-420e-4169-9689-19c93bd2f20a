# TypeScript Setup Guide for Teslametrics

## Quick Start

This guide helps you set up automatic TypeScript compilation when Node.js becomes available.

## Prerequisites

1. **Node.js** (version 18 or higher)
2. **npm** or **yarn** package manager

## Installation Steps

### 1. Install TypeScript Compiler

```bash
# Global installation (recommended for development)
npm install -g typescript

# Or local installation
npm install --save-dev typescript
```

### 2. Install Additional Development Dependencies

```bash
# Create package.json if it doesn't exist
npm init -y

# Install TypeScript and related tools
npm install --save-dev typescript @types/node

# Optional: Install ESLint for code quality
npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
```

### 3. Update package.json Scripts

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "build:ts": "tsc",
    "watch:ts": "tsc --watch",
    "clean:ts": "rimraf wwwroot/js/compiled",
    "dev:ts": "npm run clean:ts && npm run watch:ts"
  }
}
```

### 4. Development Workflow

```bash
# One-time compilation
npm run build:ts

# Watch mode (automatically recompiles on changes)
npm run watch:ts

# Development mode (clean + watch)
npm run dev:ts
```

## MSBuild Integration (Alternative)

If you prefer MSBuild integration, you can use the Microsoft.TypeScript.MSBuild package:

### 1. Install NuGet Package

```xml
<PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.7.2">
  <PrivateAssets>all</PrivateAssets>
  <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
</PackageReference>
```

### 2. Add MSBuild Properties

https://www.typescriptlang.org/docs/handbook/compiler-options-in-msbuild.html

```xml
<PropertyGroup>
  <TypeScriptNoImplicitAny>true</TypeScriptNoImplicitAny>
  <TypeScriptModuleKind>ES2020</TypeScriptModuleKind>
  <TypeScriptTarget>ES2020</TypeScriptTarget>
  <TypeScriptGeneratesDeclarations>true</TypeScriptGeneratesDeclarations>
</PropertyGroup>
```

### 3. Include TypeScript Files

```xml
<ItemGroup>
  <TypeScriptCompile Include="TypeScript\**\*.ts" />
</ItemGroup>
```

## IDE Configuration

### Visual Studio Code

Install recommended extensions:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint"
  ]
}
```

### Visual Studio

TypeScript support is built-in. Ensure you have:
- Latest Visual Studio version
- Web development workload installed
- TypeScript SDK (usually included)

## ESLint Configuration (Optional)

Create `.eslintrc.json`:

```json
{
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "warn"
  }
}
```

## Prettier Configuration (Optional)

Create `.prettierrc`:

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## Git Integration

Add to `.gitignore`:

```gitignore
# TypeScript compilation output
wwwroot/js/compiled/

# Node modules (if using npm)
node_modules/

# TypeScript cache
*.tsbuildinfo
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Build and Test

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      working-directory: src/Teslametrics.App.Web
      
    - name: Compile TypeScript
      run: npm run build:ts
      working-directory: src/Teslametrics.App.Web
      
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'
        
    - name: Build .NET project
      run: dotnet build
      working-directory: src/Teslametrics.App.Web
```

## Troubleshooting

### Common Issues

1. **"tsc command not found"**
   - Install TypeScript globally: `npm install -g typescript`
   - Or use npx: `npx tsc`

2. **Compilation errors**
   - Check `tsconfig.json` configuration
   - Verify file paths and imports
   - Check TypeScript version compatibility

3. **Module resolution issues**
   - Ensure `moduleResolution: "node"` in tsconfig.json
   - Check import paths are correct
   - Verify compiled files exist in output directory

### Performance Tips

1. **Use incremental compilation**
   ```json
   {
     "compilerOptions": {
       "incremental": true,
       "tsBuildInfoFile": ".tsbuildinfo"
     }
   }
   ```

2. **Exclude unnecessary files**
   ```json
   {
     "exclude": [
       "node_modules",
       "bin",
       "obj",
       "wwwroot/lib"
     ]
   }
   ```

3. **Use project references for large codebases**
   ```json
   {
     "references": [
       { "path": "./shared" },
       { "path": "./components" }
     ]
   }
   ```

## Next Steps

1. Set up automatic TypeScript compilation
2. Add more TypeScript modules for specific functionality
3. Implement type definitions for Blazor interop
4. Add unit tests for TypeScript code
5. Set up code quality tools (ESLint, Prettier)
6. Configure CI/CD pipeline for automatic compilation
