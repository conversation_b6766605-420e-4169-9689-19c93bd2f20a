using Blazor.DownloadFileFast.Interfaces;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.XLSXExport;

public partial class XLSXExportComponent
{
    #region [Injectables]
    [Inject]
    private IBlazorDownloadFileService _blazorDownloadFileService { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    [Parameter]
    public Guid? CityId { get; set; } = null;

    [Parameter]
    public Guid? BuildingId { get; set; } = null;

    [Parameter]
    public Guid? FloorId { get; set; } = null;

    [Parameter]
    public Guid? RoomId { get; set; } = null;

    [Parameter]
    public Guid? FridgeId { get; set; } = null;

    [Parameter]
    public IncidentType? IncidentType { get; set; }

    [Parameter]
    public bool? IsResolved { get; set; } = null;
    #endregion

    private async Task ExportAsync()
    {
        GetXLSXUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetXLSXUseCase.Query(new DateTimeOffset(DateFrom.ToUniversalTime()), new DateTimeOffset(DateTo.ToUniversalTime()), CityId, BuildingId, FloorId, RoomId, FridgeId, IncidentType, IsResolved));
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить CSV-файл из-за ошибки на сервере. Обратитесь к администратору", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case GetXLSXUseCase.Result.Success:
                try
                {
                    await _blazorDownloadFileService.DownloadFileAsync("export.xlsx", response.XLSXByteArray);
                }
                catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
                {
                }
                catch (Exception ex)
                {
                    Snackbar.Add("Не удалось скачать CSV-файл", MudBlazor.Severity.Error);
                    Logger.LogError(ex, ex.Message);
                }
                break;
            case GetXLSXUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении CSV-файла", MudBlazor.Severity.Error);
                break;
            case GetXLSXUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(XLSXExportComponent), nameof(GetXLSXUseCase));
                Snackbar.Add($"Не удалось получить CSV-файл из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(XLSXExportComponent), nameof(GetXLSXUseCase), response.Result);
                Snackbar.Add($"Не удалось получить CSV-файл из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}
