using System.Data;
using Dapper;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.XLSXExport;

public static class GetXLSXUseCase
{

    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? FridgeId, IncidentType? IncidentType, bool? IsResolved) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public byte[] XLSXByteArray { get; init; } // Город;Здание;Этаж;Помещение;Оборудование;Дата;Время;Тип происшествия (IncidentType.GetName());Статус

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(byte[] xlsx)
        {
            XLSXByteArray = xlsx;
            XLSXByteArray = xlsx;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            XLSXByteArray = [];
            XLSXByteArray = [];
        }

    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.DateTo).GreaterThan(q => q.DateFrom);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.City)
                .Select(Db.Incidents.Props.Building)
                .Select(Db.Incidents.Props.Floor)
                .Select(Db.Incidents.Props.Room)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { request.DateFrom })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { request.DateTo })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .WhereIf(request.FridgeId is not null, Db.Incidents.Props.DeviceId, ":FridgeId", SqlOperator.Equals, new { request.FridgeId })
                .WhereIf(request.IncidentType is not null, Db.Incidents.Props.IncidentType, ":IncidentType", SqlOperator.Equals, new { IncidentType = request.IncidentType.ToString() })
                .WhereIf(request.IsResolved is not null && request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNotNull)
                .WhereIf(request.IsResolved is not null && !request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            // Создаем XLSX данные
            using var memoryStream = new MemoryStream();
            using var document = SpreadsheetDocument.Create(memoryStream, SpreadsheetDocumentType.Workbook);

            // Создаем основные части документа
            var workbookPart = document.AddWorkbookPart();
            workbookPart.Workbook = new Workbook();

            var worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());

            var sheets = workbookPart.Workbook.AppendChild(new Sheets());
            var sheet = new Sheet()
            {
                Id = workbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Incidents"
            };
            sheets.Append(sheet);

            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>()!;

            // Создаем заголовки
            var headerRow = new Row() { RowIndex = 1 };
            headerRow.Append(CreateCell("A1", "Город", CellValues.String));
            headerRow.Append(CreateCell("B1", "Здание", CellValues.String));
            headerRow.Append(CreateCell("C1", "Этаж", CellValues.String));
            headerRow.Append(CreateCell("D1", "Помещение", CellValues.String));
            headerRow.Append(CreateCell("E1", "Оборудование", CellValues.String));
            headerRow.Append(CreateCell("F1", "Дата", CellValues.String));
            headerRow.Append(CreateCell("G1", "Время", CellValues.String));
            headerRow.Append(CreateCell("H1", "Тип происшествия", CellValues.String));
            headerRow.Append(CreateCell("I1", "Статус", CellValues.String));
            sheetData.Append(headerRow);

            // Добавляем данные
            uint rowIndex = 2;
            foreach (var incident in incidentModels)
            {
                var dataRow = new Row() { RowIndex = rowIndex };
                dataRow.Append(CreateCell($"A{rowIndex}", incident.City, CellValues.String));
                dataRow.Append(CreateCell($"B{rowIndex}", incident.Building, CellValues.String));
                dataRow.Append(CreateCell($"C{rowIndex}", incident.Floor.ToString(), CellValues.Number));
                dataRow.Append(CreateCell($"D{rowIndex}", incident.Room, CellValues.String));
                dataRow.Append(CreateCell($"E{rowIndex}", incident.Device, CellValues.String));
                dataRow.Append(CreateCell($"F{rowIndex}", incident.CreatedAt.ToLocalTime().ToString("dd.MM.yyyy"), CellValues.String));
                dataRow.Append(CreateCell($"G{rowIndex}", incident.CreatedAt.ToLocalTime().ToString("HH:mm:ss"), CellValues.String));
                dataRow.Append(CreateCell($"H{rowIndex}", incident.IncidentType.GetName(), CellValues.String));
                dataRow.Append(CreateCell($"I{rowIndex}", incident.ResolvedAt.HasValue ? "Завершено" : "Активное", CellValues.String));
                sheetData.Append(dataRow);
                rowIndex++;
            }

            workbookPart.Workbook.Save();
            document.Dispose();

            return new Response(memoryStream.ToArray());
        }

        private static Cell CreateCell(string cellReference, string? value, CellValues dataType)
        {
            return new Cell()
            {
                CellReference = cellReference,
                DataType = dataType,
                CellValue = new CellValue(value ?? string.Empty)
            };
        }
    }

    public record IncidentModel(Guid Id, string City, string Building, int Floor, string Room, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt);
}
