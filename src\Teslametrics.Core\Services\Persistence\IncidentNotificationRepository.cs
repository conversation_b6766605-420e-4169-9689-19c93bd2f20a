using Microsoft.EntityFrameworkCore;
using Teslametrics.Core.Abstractions;
using Teslametrics.Core.Domain.Notifications;

namespace Teslametrics.Core.Services.Persistence;

/// <summary>
/// Репозиторий для работы с уведомлениями об инцидентах
/// </summary>
public class IncidentNotificationRepository : BaseRepository<IncidentNotificationAggregate>, IIncidentNotificationRepository
{
    public IncidentNotificationRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    /// <inheritdoc />
    public Task<int> GetNotificationCountForUserAsync(Guid userId,
                                                      CancellationToken cancellationToken = default) =>
        DbContext.Set<IncidentNotificationAggregate>()
            .AsNoTracking()
            .Where(n => n.UserId == userId && !n.IsRead)
            .CountAsync(cancellationToken);

    /// <inheritdoc />
    public async Task<bool> MarkAsReadByIncidentIdAndUserIdAsync(Guid incidentId,
                                                           Guid userId,
                                                           CancellationToken cancellationToken = default)
    {
        var notification = await DbContext.Set<IncidentNotificationAggregate>()
            .AsTracking()
            .FirstOrDefaultAsync(n => n.IncidentId == incidentId && n.UserId == userId && !n.IsRead, cancellationToken);

        if (notification is not null)
        {
            notification.MarkAsRead();
            return true;
        }

        return false;
    }

    public async Task<List<Guid>> MarkAllAsReadByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var notifications = await DbContext.Set<IncidentNotificationAggregate>()
            .AsTracking()
            .Where(n => n.UserId == userId && !n.IsRead)
            .ToListAsync(cancellationToken);

        var markedNotificationIds = new List<Guid>();

        foreach (var notification in notifications)
        {
            notification.MarkAsRead();
            markedNotificationIds.Add(notification.IncidentId);
        }

        return markedNotificationIds;
    }

    /// <summary>
    /// Ограничивает количество уведомлений для пользователя до максимального значения,
    /// удаляя самые старые уведомления
    /// </summary>
    public async Task LimitNotificationsForUserAsync(Guid userId, int maxNotifications = 100, CancellationToken cancellationToken = default)
    {
        var notificationCount = await DbContext.Set<IncidentNotificationAggregate>()
            .AsNoTracking()
            .Where(n => n.UserId == userId)
            .CountAsync(cancellationToken);

        if (notificationCount > maxNotifications)
        {
            var notificationsToDelete = await DbContext.Set<IncidentNotificationAggregate>()
                .AsTracking()
                .Where(n => n.UserId == userId)
                .OrderBy(n => n.Id) // Сортируем по ID (самые старые первыми)
                .Take(notificationCount - maxNotifications)
                .ToListAsync(cancellationToken);

            DbContext.Set<IncidentNotificationAggregate>().RemoveRange(notificationsToDelete);
        }
    }
}