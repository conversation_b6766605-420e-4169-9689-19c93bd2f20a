using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services.Authentication.TwoFactorAuth;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Authentication.Account.TwoFactorAuthentication.Setup;

public static class User2faSetupUseCase
{
    public record Command(string Username) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string QRCode { get; private set; }
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;

        public Response(string qrCode, Result result)
        {
            QRCode = qrCode;
            Result = result;
        }

        public Response(Result result)
        {
            QRCode = string.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound,
        UserLockedout,
        ForceChangePassword
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Username).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ILogger<Handler> _logger;
        private readonly TwoFactorAuthService _twoFactorAuthService;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;

        public Handler(IValidator<Command> validator, ILogger<Handler> logger,
               TwoFactorAuthService twoFactorAuthService, IUserRepository userRepository,
               ITransactionManager transactionManager)
        {
            _validator = validator;
            _logger = logger;
            _twoFactorAuthService = twoFactorAuthService;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_validator.Validate(request).IsValid)
                {
                    return new Response(Result.ValidationError);
                }

                using var transaction = await _transactionManager.CreateTransactionAsync();

                var user = await _userRepository.FindByUsernameAsync(request.Username, cancellationToken);

                if (user is null)
                {
                    return new Response(Result.UserNotFound);
                }

                if (user.LockoutEnabled)
                {
                    return new Response(Result.UserLockedout);
                }

                if (user.ForcePasswordChange)
                {
                    return new Response(Result.ForceChangePassword);
                }

                user.UpdateSecretKey(_twoFactorAuthService.GenerateSecretKey());

                var qrCodeBase64 = _twoFactorAuthService.GenerateQrCodeBase64(user.SecretKey, user.Name, );

                
                await _userRepository.SaveChangesAsync(cancellationToken);

                await transaction.CommitAsync();

                return new Response(qrCodeBase64, Result.Success);
            }
            catch (Exception exc)
            {
                _logger.LogError(exc, exc.Message);
                return new Response(Result.Unknown);
            }
        }
    }
}
