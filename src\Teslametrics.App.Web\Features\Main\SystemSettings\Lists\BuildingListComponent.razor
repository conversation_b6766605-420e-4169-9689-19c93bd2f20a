@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
<MudList T="BuildingModel"
         SelectedValue="SelectedBuilding"
         SelectedValueChanged="SelectedValueChanged">
    <MudListSubheader Class="d-flex">
        Список зданий в городе
        <MudSpacer />
        <MudButton StartIcon="@Icons.Material.Outlined.Add"
                   OnClick="@(() => AddBuilding())"
                   Disabled="string.IsNullOrWhiteSpace(City?.Name)"
                   Color="Color.Primary">
            Добавить здание
        </MudButton>
    </MudListSubheader>

    @foreach (var item in Buildings)
    {
        <MudListItem Value="@item"
                     Icon="@Icons.Material.Filled.Room"
                     @key="@item">
            <ChildContent>
                <div class="d-flex flex-row align-center gap-3">
                    <div class="d-flex flex-column px-6 py-2">
                        <MudText Typo="Typo.body1">
                            Здание: @(string.IsNullOrEmpty(item.Address) ? "Не указан адрес" : item.Address)
                        </MudText>
                    </div>
                    <MudSpacer />
                    <MudTooltip Text="Удалить здание">
                        <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                       Color="Color.Warning"
                                       OnClick="@(() => RemoveBuilding(item))" />
                    </MudTooltip>
                </div>
            </ChildContent>
        </MudListItem>
    }

    @if (Buildings.Count == 0)
    {
        <div class="pa-4">
            <NoItemsFoundComponent HasItems="false" />
        </div>
    }
</MudList>
