﻿@using Teslametrics.App.Web.Components.MSEPlayer
@using Teslametrics.MediaServer.Orleans.Camera
@inherits InteractiveBaseComponent
@attribute [Route("/demo/typescript-test")]

<PageTitle>Демонстрация временной шкалы MSE плеера</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large"
              Class="mt-4">
    <MudText Typo="Typo.h4"
             Class="mb-4">Демонстрация временной шкалы MSE плеера</MudText>

    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">Параметры камеры</MudText>

        <MudGrid>
            <MudItem xs="12"
                     sm="6">
                <MudTextField @bind-Value="CameraId"
                              Label="ID камеры"
                              Variant="Variant.Outlined"
                              HelperText="Введите GUID камеры"
                              Class="mb-2" />
            </MudItem>

            <MudItem xs="12"
                     sm="6">
                <MudSelect @bind-Value="StreamType"
                           Label="Тип потока"
                           Variant="Variant.Outlined"
                           Class="mb-2">
                    <MudSelectItem Value="1">Archive</MudSelectItem>
                    <MudSelectItem Value="2">View</MudSelectItem>
                    <MudSelectItem Value="4">Public</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>

        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   OnClick="ApplySettings"
                   Class="mt-2">
            Применить настройки
        </MudButton>
    </MudPaper>

    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">MSE плеер с временной шкалой</MudText>

        @if (string.IsNullOrEmpty(AppliedCameraId))
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Введите ID камеры и нажмите "Применить настройки" для просмотра потока с камерой.
            </MudText>
        }
        else
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Просмотр потока с камеры ID: @AppliedCameraId, тип потока: @Teslametrics.MediaServer.Orleans.Camera.StreamType.View.GetName()
            </MudText>

            <!-- MSE плеер -->
            <div class="mb-4">
                @* <MsePlayer @ref="msePlayer"
                           CameraId="@Guid.Parse(AppliedCameraId)"
                           Type="@Teslametrics.MediaServer.Orleans.Camera.StreamType.View">
                    <ToolbarContent>
                        <PlayToggleButton Player="context" />
                        <VolumeComponent Player="context"
                                         ShowForced="true" />
                        <SeekToLiveButton Player="context" />
                        <MudSpacer />
                        <FullscreenToggleButton Player="context" />
                    </ToolbarContent>
                </MsePlayer> *@
                <TeslaPlayer @ref="_player"
                             CameraId="@Guid.Parse(AppliedCameraId)"
                             Type="@Teslametrics.MediaServer.Orleans.Camera.StreamType.View">
                    <ToolbarContent>
                        <PlayPausePluginComponent />
                    </ToolbarContent>
                </TeslaPlayer>
            </div>
        }
    </MudPaper>
</MudContainer>
