using System.Data;
using System.Globalization;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.CSVExport;

public static class GetCSVUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? FridgeId, IncidentType? IncidentType, bool? IsResolved) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public byte[] CSVByteArray { get; init; } // Город;Здание;Этаж;Помещение;Оборудование;Дата;Время;Тип происшествия (IncidentType.GetName());Статус

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(byte[] csv)
        {
            CSVByteArray = csv;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            CSVByteArray = [];
        }

    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.DateTo).GreaterThan(q => q.DateFrom);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.City)
                .Select(Db.Incidents.Props.Building)
                .Select(Db.Incidents.Props.Floor)
                .Select(Db.Incidents.Props.Room)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { request.DateFrom })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { request.DateTo })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .WhereIf(request.FridgeId is not null, Db.Incidents.Props.DeviceId, ":FridgeId", SqlOperator.Equals, new { request.FridgeId })
                .WhereIf(request.IncidentType is not null, Db.Incidents.Props.IncidentType, ":IncidentType", SqlOperator.Equals, new { IncidentType = request.IncidentType.ToString() })
                .WhereIf(request.IsResolved is not null && request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNotNull)
                .WhereIf(request.IsResolved is not null && !request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            // Преобразуем в модель для экспорта
            var exportModels = incidentModels.Select(i => new IncidentExportModel(
                City: i.City,
                Building: i.Building,
                Floor: i.Floor,
                Room: i.Room,
                Device: i.Device,
                Date: i.CreatedAt.ToLocalTime().ToString("dd.MM.yyyy"),
                Time: i.CreatedAt.ToLocalTime().ToString("HH:mm:ss"),
                IncidentTypeName: i.IncidentType.GetName() ?? string.Empty,
                Status: i.ResolvedAt.HasValue ? "Завершено" : "Активное"
            ));

            // Создаем CSV данные
            using var memoryStream = new MemoryStream();
            using var streamWriter = new StreamWriter(memoryStream, Encoding.UTF8);
            using var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture);

            csvWriter.Context.RegisterClassMap<IncidentExportCsvMap>();
            csvWriter.WriteRecords(exportModels);
            streamWriter.Flush();

            return new Response(memoryStream.ToArray());
        }
    }

    public record IncidentModel(Guid Id, string City, string Building, int Floor, string Room, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt);

    public record IncidentExportModel(string City, string Building, int Floor, string Room, string Device, string Date, string Time, string IncidentTypeName, string Status);

    public class IncidentExportCsvMap : ClassMap<IncidentExportModel>
    {
        public IncidentExportCsvMap()
        {
            Map(m => m.City).Name("Город");
            Map(m => m.Building).Name("Здание");
            Map(m => m.Floor).Name("Этаж");
            Map(m => m.Room).Name("Помещение");
            Map(m => m.Device).Name("Оборудование");
            Map(m => m.Date).Name("Дата");
            Map(m => m.Time).Name("Время");
            Map(m => m.IncidentTypeName).Name("Тип происшествия");
            Map(m => m.Status).Name("Статус");
        }
    }
}