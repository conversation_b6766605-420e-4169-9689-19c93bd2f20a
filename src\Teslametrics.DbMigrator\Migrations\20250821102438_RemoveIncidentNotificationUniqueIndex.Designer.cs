﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Teslametrics.Core.Services.Persistence;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    [DbContext(typeof(CommandAppDbContext))]
    [Migration("20250821102438_RemoveIncidentNotificationUniqueIndex")]
    partial class RemoveIncidentNotificationUniqueIndex
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.CameraQuotaEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Limit")
                        .HasColumnType("integer")
                        .HasColumnName("limit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid?>("PresetId")
                        .HasColumnType("uuid")
                        .HasColumnName("preset_id");

                    b.Property<int>("RetentionPeriodDays")
                        .HasColumnType("integer")
                        .HasColumnName("retention_period_days");

                    b.Property<int>("StorageLimitMb")
                        .HasColumnType("integer")
                        .HasColumnName("storage_limit_mb");

                    b.HasKey("Id")
                        .HasName("pk_camera_quotas");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("ix_camera_quotas_organization_id");

                    b.ToTable("camera_quotas", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_id");

                    b.HasKey("Id")
                        .HasName("pk_organizations");

                    b.ToTable("organizations", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_admin");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid?>("organization_id")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.HasKey("Id")
                        .HasName("pk_roles");

                    b.HasIndex("organization_id")
                        .HasDatabaseName("ix_roles_organization_id");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.RolePermissionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.ComplexProperty<Dictionary<string, object>>("ResourcePermission", "Teslametrics.Core.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission", b1 =>
                        {
                            b1.ComplexProperty<Dictionary<string, object>>("Permission", "Teslametrics.Core.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission.Permission#Permission", b2 =>
                                {
                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("permission");
                                });

                            b1.ComplexProperty<Dictionary<string, object>>("ResourceId", "Teslametrics.Core.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission.ResourceId#ResourceId", b2 =>
                                {
                                    b2.Property<Guid>("Value")
                                        .HasColumnType("uuid")
                                        .HasColumnName("resource_id");
                                });
                        });

                    b.HasKey("Id")
                        .HasName("pk_role_permissions");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_role_permissions_role_id");

                    b.ToTable("role_permissions", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("")
                        .HasColumnName("email");

                    b.Property<bool>("EmailNotificationEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("email_notification_enabled");

                    b.Property<bool>("ForcePasswordChange")
                        .HasColumnType("boolean")
                        .HasColumnName("force_password_change");

                    b.Property<bool>("Is2faEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is2fa_enabled");

                    b.Property<DateTimeOffset?>("LastLogInTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_log_in_time");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("password");

                    b.Property<string>("SecretKey")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("secret_key");

                    b.Property<bool>("Setup2FAIsCompleted")
                        .HasColumnType("boolean")
                        .HasColumnName("setup2fa_is_completed");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserOrganizationEntity", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.HasKey("UserId", "OrganizationId")
                        .HasName("pk_user_organizations");

                    b.ToTable("user_organizations", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserRoleEntity", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId")
                        .HasName("pk_user_roles");

                    b.ToTable("user_roles", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.CameraPresets.PresetAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ArchiveStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("archive_stream_config");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("PublicStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_stream_config");

                    b.Property<string>("ViewStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("view_stream_config");

                    b.HasKey("Id")
                        .HasName("pk_presets");

                    b.ToTable("presets", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.CameraViews.CameraViewAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cells")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cells");

                    b.Property<short>("ColumnCount")
                        .HasColumnType("smallint")
                        .HasColumnName("column_count");

                    b.Property<int>("GridType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("grid_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<short>("RowCount")
                        .HasColumnType("smallint")
                        .HasColumnName("row_count");

                    b.HasKey("Id")
                        .HasName("pk_camera_views");

                    b.ToTable("camera_views", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.Cameras.CameraAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ArchiveUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("archive_uri");

                    b.Property<bool>("AutoStart")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_start");

                    b.Property<Guid>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<bool>("IsBlocked")
                        .HasColumnType("boolean")
                        .HasColumnName("is_blocked");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision")
                        .HasColumnName("latitude");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<bool>("OnvifEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("onvif_enabled");

                    b.Property<string>("OnvifSettings")
                        .HasColumnType("text")
                        .HasColumnName("onvif_settings");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<string>("PublicUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_uri");

                    b.Property<Guid?>("QuotaId")
                        .HasColumnType("uuid")
                        .HasColumnName("quota_id");

                    b.Property<TimeSpan>("TimeZone")
                        .HasColumnType("interval")
                        .HasColumnName("time_zone");

                    b.Property<string>("ViewUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("view_uri");

                    b.HasKey("Id")
                        .HasName("pk_cameras");

                    b.ToTable("cameras", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.Folders.FolderAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_id");

                    b.HasKey("Id")
                        .HasName("pk_folders");

                    b.ToTable("folders", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.Incidents.IncidentAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Building")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("building");

                    b.Property<Guid>("BuildingId")
                        .HasColumnType("uuid")
                        .HasColumnName("building_id");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("city");

                    b.Property<Guid>("CityId")
                        .HasColumnType("uuid")
                        .HasColumnName("city_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Device")
                        .HasColumnType("text")
                        .HasColumnName("device");

                    b.Property<Guid?>("DeviceId")
                        .HasColumnType("uuid")
                        .HasColumnName("device_id");

                    b.Property<int>("Floor")
                        .HasColumnType("integer")
                        .HasColumnName("floor");

                    b.Property<Guid>("FloorId")
                        .HasColumnType("uuid")
                        .HasColumnName("floor_id");

                    b.Property<string>("IncidentType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("incident_type");

                    b.Property<DateTimeOffset?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("resolved_at");

                    b.Property<string>("Room")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("room");

                    b.Property<Guid>("RoomId")
                        .HasColumnType("uuid")
                        .HasColumnName("room_id");

                    b.Property<Guid>("SensorId")
                        .HasColumnType("uuid")
                        .HasColumnName("sensor_id");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("topic");

                    b.HasKey("Id")
                        .HasName("pk_incidents");

                    b.HasIndex("Building")
                        .HasDatabaseName("ix_incidents_building");

                    b.HasIndex("BuildingId")
                        .HasDatabaseName("ix_incidents_building_id");

                    b.HasIndex("City")
                        .HasDatabaseName("ix_incidents_city");

                    b.HasIndex("CityId")
                        .HasDatabaseName("ix_incidents_city_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_incidents_created_at");

                    b.HasIndex("Device")
                        .HasDatabaseName("ix_incidents_device");

                    b.HasIndex("DeviceId")
                        .HasDatabaseName("ix_incidents_device_id");

                    b.HasIndex("Floor")
                        .HasDatabaseName("ix_incidents_floor");

                    b.HasIndex("FloorId")
                        .HasDatabaseName("ix_incidents_floor_id");

                    b.HasIndex("IncidentType")
                        .HasDatabaseName("ix_incidents_incident_type");

                    b.HasIndex("ResolvedAt")
                        .HasDatabaseName("ix_incidents_resolved_at");

                    b.HasIndex("Room")
                        .HasDatabaseName("ix_incidents_room");

                    b.HasIndex("RoomId")
                        .HasDatabaseName("ix_incidents_room_id");

                    b.HasIndex("SensorId")
                        .HasDatabaseName("ix_incidents_sensor_id");

                    b.HasIndex("Topic")
                        .HasDatabaseName("ix_incidents_topic");

                    b.HasIndex("City", "Building", "Floor", "Room")
                        .HasDatabaseName("ix_incidents_city_building_floor_room");

                    b.ToTable("incidents", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.Notifications.IncidentNotificationAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("IncidentId")
                        .HasColumnType("uuid")
                        .HasColumnName("incident_id");

                    b.Property<string>("NotificationType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("notification_type");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_incident_notifications");

                    b.HasIndex("IncidentId")
                        .HasDatabaseName("ix_incident_notifications_incident_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_incident_notifications_user_id");

                    b.ToTable("incident_notifications", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.PublicLinks.PublicLinkAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CameraId")
                        .HasColumnType("uuid")
                        .HasColumnName("camera_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_public_links");

                    b.ToTable("public_links", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Services.Outbox.OutboxMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("content");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("event_type");

                    b.HasKey("Id")
                        .HasName("pk_outbox_messages");

                    b.ToTable("outbox_messages", (string)null);
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.CameraQuotaEntity", b =>
                {
                    b.HasOne("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationAggregate", null)
                        .WithMany("CameraQuotas")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_camera_quotas_organizations_organization_id");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.HasOne("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationAggregate", null)
                        .WithMany("Roles")
                        .HasForeignKey("organization_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_roles_organizations_organization_id");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.RolePermissionEntity", b =>
                {
                    b.HasOne("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationRoleEntity", null)
                        .WithMany("ResourcePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permissions_roles_role_id");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserOrganizationEntity", b =>
                {
                    b.HasOne("Teslametrics.Core.Domain.AccessControl.Users.UserAggregate", null)
                        .WithMany("Organizations")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_organizations_users_user_id");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserRoleEntity", b =>
                {
                    b.HasOne("Teslametrics.Core.Domain.AccessControl.Users.UserAggregate", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_roles_users_user_id");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationAggregate", b =>
                {
                    b.Navigation("CameraQuotas");

                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.Navigation("ResourcePermissions");
                });

            modelBuilder.Entity("Teslametrics.Core.Domain.AccessControl.Users.UserAggregate", b =>
                {
                    b.Navigation("Organizations");

                    b.Navigation("Roles");
                });
#pragma warning restore 612, 618
        }
    }
}
