namespace Teslametrics.App.Web.Components.TeslaPlayer;

public partial class DemoTest
{
    private string CameraId { get; set; } = string.Empty;
    private int StreamType { get; set; } = 2; // View по умолчанию
    private string AppliedCameraId { get; set; } = string.Empty;
    private IPlayer? _player;
    private void ApplySettings()
    {
        if (Guid.TryParse(CameraId, out _))
        {
            AppliedCameraId = CameraId;
            StateHasChanged();
        }
        else
        {
            Snackbar.Add("Введите корректный GUID камеры", MudBlazor.Severity.Warning);
        }
    }
}
