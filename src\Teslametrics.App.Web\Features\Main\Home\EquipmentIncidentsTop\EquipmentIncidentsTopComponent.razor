﻿@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="d_contents">
    <MudPaper Elevation="0"
              Outlined="true"
              Class="@($"pa-4 mud-height-full paper {Class}")">
        <div class="d-flex flex-column gap-1 mb-3 pa-1">
            <MudText Typo="Typo.subtitle1">Наиболее проблемное оборудование</MudText>
            <MudText Typo="Typo.body1"
                     Class="tooltip">За 30 дней</MudText>
        </div>
        @if (_response?.Incidents.Count > 0)
        {
            <div id="equipment-incidents-top-chart"
                 style="height: 300px;"></div>
            <div class="legends d-flex align-center gap-3 px-3">
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.WirenboardDisconnected)"
                                     Type="@IncidentType.WirenboardDisconnected" />
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.Temperature)"
                                     Type="@IncidentType.Temperature" />
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.Humidity)"
                                     Type="@IncidentType.Humidity" />
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.Leak)"
                                     Type="@IncidentType.Leak" />
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.Door)"
                                     Type="@IncidentType.Door" />
                <LegendItemComponent IsVisible="_incidentTypes.Contains(IncidentType.Power)"
                                     Type="@IncidentType.Power" />
            </div>
        }
        else
        {
            <div class="d-flex flex-column align-center justify-center no_data gap-2">
                <div class="icon_container bg_success br_6 d-flex align-center justify-center">
                    <MudIcon Icon="@TeslaIcons.Outlined.CheckCircle"
                             Style="font-size: 16px;"
                             Color="Color.Success" />
                </div>
                <MudText Typo="Typo.body1"
                         Class="color_text_placeholder">Нет происшествий за выбранный период</MudText>
            </div>
        }
    </MudPaper>
</div>
<template id="equipmentIncidentsTopTooltipTemplate">
    <div class="equipment-custom-tooltip">
        <div class="header mt-2 mx-2">Всего<span class="header-value"></span></div>
        <div class="incidents px-2 pb-2 d-flex flex-column"></div>
    </div>
</template>
<template id="equipmentIncidentsTopTooltipIncidentTemplate">
    <div class="equipment-custom-tooltip-incident d-flex gap-2 align-center">
        <div class="dot"></div>
        <span class="text"></span>
        <span class="value"></span>
    </div>
</template>