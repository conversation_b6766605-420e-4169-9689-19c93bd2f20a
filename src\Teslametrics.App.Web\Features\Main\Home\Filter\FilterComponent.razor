﻿@using Teslametrics.App.Web.Features.Main.Home.Filter.DateFilter
@using Teslametrics.Shared

<MudPaper Elevation="0"
          Outlined="false"
          Class="@($"mud-width-full d-flex gap-4 flex-column {Class}")">
    <div class="d_contents">
        <MudStack Row="true"
                  AlignItems="AlignItems.Center"
                  Class="filters_header">
            <MudText Typo="Typo.subtitle2">Фильтры</MudText>
        </MudStack>
    </div>
    <div class="d-flex align-center gap-3">
        <DateFilterComponent DateRange="@_dateRange"
                             DateRangeChanged="OnDateRangeChanged" />
    </div>
</MudPaper>
