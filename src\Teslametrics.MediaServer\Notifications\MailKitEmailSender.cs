using MailKit.Net.Smtp;
using Microsoft.Extensions.Options;
using MimeKit;

namespace Teslametrics.MediaServer.Notifications;

/// <summary>
/// MailKit реализация отправки email (лучше поддерживает различные SMTP конфигурации)
/// </summary>
public class MailKitEmailSender : IEmailSender
{
    private readonly EmailSenderSettings _settings;
    private readonly ILogger<MailKitEmailSender> _logger;

    public MailKitEmailSender(IOptions<EmailSenderSettings> settings, ILogger<MailKitEmailSender> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task SendAsync(string to, string subject, string body, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_settings.Host) || string.IsNullOrEmpty(_settings.FromAddress))
        {
            _logger.LogWarning("Email sender is not configured. Skipping email sending.");
            return;
        }

        try
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_settings.FromDisplayName ?? "Teslametrics", _settings.FromAddress));
            message.To.Add(new MailboxAddress("", to));
            message.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = body
            };
            message.Body = bodyBuilder.ToMessageBody();

            using var client = new SmtpClient();
            var port = _settings.Port ?? 587;

            // Подключаемся с таймаутом
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(30));

            await client.ConnectAsync(_settings.Host, port, _settings.EnableSsl ?? true, timeoutCts.Token);

            // Аутентификация если есть учетные данные
            if (!string.IsNullOrEmpty(_settings.UserName) && !string.IsNullOrEmpty(_settings.Password))
            {
                await client.AuthenticateAsync(_settings.UserName, _settings.Password, timeoutCts.Token);
            }

            // Отправляем сообщение
            await client.SendAsync(message, timeoutCts.Token);
            _logger.LogInformation("Email sent successfully to {To}", to);

            await client.DisconnectAsync(true, CancellationToken.None);
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("Email sending to {To} was cancelled", to);
            throw;
        }
        catch (OperationCanceledException)
        {
            _logger.LogError("Email sending to {To} timed out", to);
            throw new TimeoutException($"Email sending to {to} timed out after 30 seconds");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {To}", to);
            throw;
        }
    }
}