/**
 * Возможные режимы работы видеоплеера.
 * - `'live'`: режим реального времени
 * - `'point'`: воспроизведение с определённой временной точки
 * - `'range'`: воспроизведение от начала до конца диапазона
 */
export const enum ViewMode {
	Live = "live",
	VOD = "VOD",
	Range = "range",
}

/**
 * Конфигурация режима плеера.
 */
export interface ModeConfig {
	/** Web-socket endpoint для SignalR */
	wsPath: string;

	/**
	 * MIME-тип потока для MediaSource.
	 * @example "video/mp2t;codecs=avc1.640028"
	 */
	mime: string;

	/**
	 * Сколько секунд медиаданных хранить в буфере.
	 * Используется для trim-очистки.
	 */
	keep: number;

	/**
	 * Автоматически ли переподключаться при обрыве соединения.
	 */
	autoreconnect: boolean;
}

/**
 * Набор предопределённых режимов плеера.
 * Возвращает объект конфигурации `ModeConfig` на основе входных параметров.
 */
export declare const MODES: {
	/**
	 * Режим живого видео в реальном времени.
	 * @returns Конфигурация для режима live
	 */
	live(): ModeConfig;

	/**
	 * Воспроизведение с определённой временной точки.
	 * @param opts.start Дата или строка начала
	 * @returns Конфигурация для режима point
	 */
	point(opts: { start: Date | string }): ModeConfig;

	/**
	 * Воспроизведение диапазона по времени.
	 * @param opts.start Дата начала диапазона
	 * @param opts.end Дата окончания диапазона
	 * @returns Конфигурация для режима range
	 */
	range(opts: { start: Date | string; end: Date | string }): ModeConfig;
};
