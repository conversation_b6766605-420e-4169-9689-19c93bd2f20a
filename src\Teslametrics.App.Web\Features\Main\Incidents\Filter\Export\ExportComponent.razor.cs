using Microsoft.AspNetCore.Components;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export;

public partial class ExportComponent
{
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    [Parameter]
    public Guid? CityId { get; set; } = null;

    [Parameter]
    public Guid? BuildingId { get; set; } = null;

    [Parameter]
    public Guid? FloorId { get; set; } = null;

    [Parameter]
    public Guid? RoomId { get; set; } = null;

    [Parameter]
    public Guid? FridgeId { get; set; } = null;

    [Parameter]
    public IncidentType? IncidentType { get; set; }

    [Parameter]
    public bool? IsResolved { get; set; } = null;
}
