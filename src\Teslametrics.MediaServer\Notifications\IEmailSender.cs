namespace Teslametrics.MediaServer.Notifications;

/// <summary>
/// Интерфейс для отправки email сообщений
/// </summary>
public interface IEmailSender
{
    /// <summary>
    /// Отправляет email сообщение
    /// </summary>
    /// <param name="to">Email получателя</param>
    /// <param name="subject">Тема сообщения</param>
    /// <param name="body">Тело сообщения (HTML)</param>
    /// <param name="cancellationToken">Токен отмены</param>
    Task SendAsync(string to, string subject, string body, CancellationToken cancellationToken = default);
}
