.no_data {
    min-height: 250px;
}

.icon_container {
    width: 28px;
    height: 28px;
}

::deep .tooltip {
    color: var(--color-text-placeholder);
}

.mud_theme_dark div ::deep .paper {
    background: transparent;
    border-color: transparent;
}

.equipment-custom-tooltip {
    position: absolute;
    background: var(--mud-palette-surface);
    border-radius: 8px;
    font-family: Inter, sans-serif;
    pointer-events: none;
    z-index: 1000;
    transition: opacity 0.2s ease-in-out;
    border: 1px solid var(--color-stroke-stroke);
    white-space: nowrap;
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 192px;
}
.equipment-custom-tooltip > .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--color-stroke-light);
}
.equipment-custom-tooltip > .header,
.equipment-custom-tooltip > .header span,
.equipment-custom-tooltip-incident{
    font-size: 10px;
    font-weight: 400;
    line-height: 110%;
}

.equipment-custom-tooltip > .incidents {
    gap: 6px;
}
.equipment-custom-tooltip-incident .dot {
    min-width: 8px;
    min-height: 8px;
    max-width: 8px;
    max-height: 8px;
    display: block;
    border-radius: 4px;
}

.equipment-custom-tooltip-incident[data-type="Temperature"] .dot {
    background: var(--color-incident-type-temperature);
}
.equipment-custom-tooltip-incident[data-type="Door"] .dot {
    background: var(--color-incident-type-door);
}
.equipment-custom-tooltip-incident[data-type="Humidity"] .dot {
    background: var(--color-incident-type-humidity);
}
.equipment-custom-tooltip-incident[data-type="Leak"] .dot {
    background: var(--color-incident-type-leak);
}
.equipment-custom-tooltip-incident[data-type="Power"] .dot {
    background: var(--color-incident-type-power);
}
.equipment-custom-tooltip-incident[data-type="WirenboardDisconnected"] .dot {
    background: var(--color-incident-type-wirenboard-disconnected);
}
.equipment-custom-tooltip-incident > .text {
    width: -webkit-fill-available;
}