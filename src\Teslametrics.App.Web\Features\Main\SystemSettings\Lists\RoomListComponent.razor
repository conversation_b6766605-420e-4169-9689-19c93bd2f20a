@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
<MudList T="RoomModel"
         SelectedValue="SelectedRoom"
         SelectedValueChanged="SelectedValueChanged">
    <MudListSubheader Class="d-flex">
        Список комнат на этаже
        <MudSpacer />
        <MudButton StartIcon="@Icons.Material.Outlined.Add"
                   OnClick="@(() => AddRoom())"
                   Disabled="IsAddRoomDisabled"
                   Color="Color.Primary">
            Добавить комнату
        </MudButton>
    </MudListSubheader>

    @foreach (var item in Rooms)
    {
        <MudListItem Value="@item"
                     Icon="@Icons.Material.Filled.Room"
                     @key="@item">
            <ChildContent>
                <div class="d-flex flex-row align-center gap-3">
                    <div class="d-flex flex-column px-6 py-2">
                        <MudText Typo="Typo.body1">
                            Комната: @item.Name
                        </MudText>
                    </div>
                    <MudSpacer />
                    <MudTooltip Text="Удалить комнату">
                        <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                       Color="Color.Warning"
                                       OnClick="@(() => RemoveRoom(item))" />
                    </MudTooltip>
                </div>
            </ChildContent>
        </MudListItem>
    }

    @if (Rooms.Count == 0)
    {
        <div class="pa-4">
            <NoItemsFoundComponent HasItems="false" />
        </div>
    }
</MudList>