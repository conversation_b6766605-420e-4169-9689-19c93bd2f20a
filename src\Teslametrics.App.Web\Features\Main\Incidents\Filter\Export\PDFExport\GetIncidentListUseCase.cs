using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.PDFExport;

public static class GetIncidentListUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? DeviceId, IncidentType? IncidentType, bool? IsResolved) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string? CityName { get; init; }
        public string? BuildingName { get; init; }
        public string? FloorName { get; init; }
        public string? RoomName { get; init; }
        public string? DeviceName { get; init; }

        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents, string? cityName = null, string? buildingName = null, string? floorName = null, string? roomName = null, string? deviceName = null)
        {
            CityName = cityName;
            BuildingName = buildingName;
            FloorName = floorName;
            RoomName = roomName;
            DeviceName = deviceName;

            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        public record Incident
        {
            public Guid Id { get; init; }
            public string City { get; init; }
            public string Address { get; init; }
            public int Floor { get; init; }
            public string Room { get; init; }
            public string Device { get; init; }
            public DateTimeOffset Date { get; init; }
            public string Time => Date.ToString("HH:mm");
            public IncidentType IncidentType { get; init; }
            public bool IsResolved { get; init; }

            public Incident(Guid id,
                            string city,
                            string address,
                            int floor,
                            string room,
                            string device,
                            DateTimeOffset date,
                            IncidentType incidentType,
                            bool isResolved)
            {
                Id = id;
                City = city;
                Address = address;
                Floor = floor;
                Room = room;
                Device = device;
                Date = date;
                IncidentType = incidentType;
                IsResolved = isResolved;
            }
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.DateTo).GreaterThan(q => q.DateFrom);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            Teslametrics.Shared.City? city = null;
            Teslametrics.Shared.Building? building = null;
            Teslametrics.Shared.Floor? floor = null;
            Teslametrics.Shared.Room? room = null;
            Teslametrics.Shared.Fridge? device = null;

            if (request.CityId is not null)
            {
                var planTemplate = SqlQueryBuilder.Create()
                    .Select(Db.Plans.Props.Page)
                    .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

                var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(planTemplate.RawSql);

                if (string.IsNullOrEmpty(pageJson))
                {
                    return new Response(Result.PlanNotFound);
                }

                var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

                city = page.Cities.FirstOrDefault(c => c.Id == request.CityId);

                if (request.BuildingId is not null && city is not null)
                {
                    building = city.Buildings.FirstOrDefault(b => b.Id == request.BuildingId);

                    if (request.FloorId is not null && building is not null)
                    {
                        floor = building.Floors.FirstOrDefault(f => f.Id == request.FloorId);

                        if (request.RoomId is not null && floor is not null)
                        {
                            room = floor.Rooms.FirstOrDefault(r => r.Id == request.RoomId);

                            if (request.DeviceId is not null && room is not null)
                            {
                                device = room.Fridges.FirstOrDefault(f => f.Id == request.DeviceId);
                            }
                        }
                    }
                }
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.City)
                .Select(Db.Incidents.Props.Building)
                .Select(Db.Incidents.Props.Floor)
                .Select(Db.Incidents.Props.Room)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { request.DateFrom })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { request.DateTo })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .WhereIf(request.DeviceId is not null, Db.Incidents.Props.DeviceId, ":DeviceId", SqlOperator.Equals, new { request.DeviceId })
                .WhereIf(request.IncidentType is not null, Db.Incidents.Props.IncidentType, ":IncidentType", SqlOperator.Equals, new { IncidentType = request.IncidentType.ToString() })
                .WhereIf(request.IsResolved is not null && request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNotNull)
                .WhereIf(request.IsResolved is not null && !request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var incidents = incidentModels.OrderByDescending(i => i.CreatedAt).Select(i => new Response.Incident(
                i.Id,
                i.City,
                i.Building,
                i.Floor,
                i.Room,
                i.Device,
                i.CreatedAt.ToLocalTime(),
                i.IncidentType,
                i.ResolvedAt.HasValue
            )).ToList();

            return new Response(incidents,
                city?.Name,
                building?.Address,
                floor?.Number.ToString(),
                room?.Name,
                device?.Name
            );
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid Id, string City, string Building, int Floor, string Room, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt);
}