using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using Teslametrics.App.Web.Components.MSEPlayer;
using Teslametrics.MediaServer.Orleans.Camera;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Components.TeslaPlayer;

public partial class TeslaPlayer : IPlayer, IAsyncDisposable
{
    // Reference to the video element
    private ElementReference? _videoElement;

    // Текущая громкость (0.0 - 1.0)
    private float _volume = 1.0f;
    public float Volume => _volume;

    private IJSObjectReference? _playerJsModule;
    private DotNetObjectReference<TeslaPlayer>? _objRef;
    //private bool _isStreamActive = false;

    #region [Injectables]
    [Inject]
    private ISnackbar Snackbar { get; set; } = null!;

    [Inject]
    private IJSRuntime Js { get; set; } = null!;
    #endregion

    #region [Parameters]

    [Parameter]
    public bool Muted { get; set; } = true;

    [Parameter]
    public bool Autoplay { get; set; } = true;

    [Parameter]
    public RenderFragment<IPlayer>? ToolbarContent { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public StreamType Type { get; set; }
    #endregion

    #region [JSInvokables]
    [JSInvokable]
    public void OnCameraError(string errorMessage)
    {
        Logger.LogError("Ошибка в видеоплеере камеры {CameraId}: {ErrorMessage}", CameraId, errorMessage);
        Snackbar.Add($"Ошибка видеоплеера: {errorMessage}", Severity.Error);
    }
    #endregion

    #region [Public API]
    public async Task ResumeStream()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule)); // Стоит ли иметь другой тип исключения?

        await _playerJsModule.InvokeVoidAsync("resumeStream", CameraId);
    }

    public async Task PauseStream()
    {
        if (_playerJsModule is null) throw new ArgumentNullException(nameof(_playerJsModule)); // Стоит ли иметь другой тип исключения?

        await _playerJsModule.InvokeVoidAsync("pauseStream", CameraId);
    }

    /// <summary>
    /// Полностью останавливает поток и закрывает соединение с сервером
    /// </summary>
    /// <throws>JSDisconnectedException</throws>
    /// <throws>Exception</throws>
    public async Task StopStream()
    {
        if (_playerJsModule is null) return; // Если модуль не инициализирован, ничего делать не нужно

        try
        {
            await _playerJsModule.InvokeVoidAsync("stopStream", CameraId);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при остановке потока: {Message}", ex.Message);
            // Не показываем Snackbar, так как это может происходить при закрытии страницы
        }
    }
    #endregion

    public async Task AddPluginAsync(string pluginName, IJSObjectReference plugin)
    {
        ArgumentNullException.ThrowIfNull(plugin);

        try
        {
            await _playerJsModule!.InvokeVoidAsync("addPlugin", CameraId, plugin);
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, ничего делать не нужно
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при добавлении плагина: {Message}", ex.Message);
        }
    }

    public Task RemovePluginAsync(string pluginName)
    {
        throw new NotImplementedException();
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Сначала отменяем регистрацию callback'а
            if (_playerJsModule is not null)
            {
                try
                {
                    await _playerJsModule.InvokeVoidAsync("unregisterStatusCallback", CameraId);
                }
                catch (JSDisconnectedException)
                {
                    // Если соединение с JS уже разорвано, ничего делать не нужно
                }
            }

            // Затем останавливаем поток и закрываем SignalR-соединение
            await StopStream();

            // Затем освобождаем ресурсы JS-модуля
            if (_playerJsModule is not null)
            {
                await _playerJsModule.DisposeAsync();
            }

            // Освобождаем ссылку на .NET-объект
            _objRef?.Dispose();
        }
        catch (JSDisconnectedException)
        {
            // Если соединение с JS уже разорвано, возвращаем null
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при освобождении ресурсов: {Message}", ex.Message);
        }

        GC.SuppressFinalize(this);
    }

    protected override void OnInitialized()
    {
        if (!Muted && Autoplay) throw new ArgumentException("Невозможно создать плеер с autoplay и с muted = false");

        _objRef = DotNetObjectReference.Create(this);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            try
            {
                // Загружаем основной модуль плеера
                _playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./js/compiled/Components/TeslaPlayer/TeslaPlayer.razor-module.js");

                if (_playerJsModule is not null)
                {
                    // Инициализируем плеер
                    await _playerJsModule.InvokeVoidAsync("initializePlayer",
                            _videoElement,
                            CameraId,
                            "live", // "live" | "point" | "range"
                            null, // startIso
                            null, // endIso
                            null  // plugins
                    ); // Доделать режимы воспроизведения
                }
            }
            catch (JSDisconnectedException)
            {
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Ошибка при загрузке скриптов: {Message}", ex.Message);
                Snackbar.Add("Не удалось загрузить необходимые файлы скриптов, плеер будет недоступен!", Severity.Error);
            }
        }
    }

    protected override bool ShouldRender() // После того, как компонент отрендерился нам уже не нужны его обновления. Отрубаем их.
    {
        return false;
    }
}
