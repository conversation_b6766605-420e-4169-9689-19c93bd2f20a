@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
@implements IAsyncDisposable
<div class="d_contents">
    <MudButton Variant="Variant.Filled"
               StartIcon="@TeslaIcons.Actions.Import"
               OnClick="ExportChartsAsync"
               Disabled="@IsLoading"
               Class="ml-2 button">
        @if (IsLoading)
        {
            <MudProgressCircular Size="Size.Small"
                                 Indeterminate="true"
                                 Class="mr-2" />
            <span>Экспорт...</span>
        }
        else
        {
            <span>Скачать отчет</span>
        }
    </MudButton>
</div>
