@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
@using Teslametrics.App.Web.Features.Main.SystemSettings.CameraList
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorForm
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorList
@using Teslametrics.App.Web.Components
@inherits InteractiveBaseComponent

<div class="pa-4 d-flex flex-column gap-3">
    @if (Entity is CityModel city)
    {
        <MudTextField T="string"
                      @bind-Value="@city.Name"
                      @bind-Value:after="@(() => OnEntityChanged.InvokeAsync())"
                      Immediate="true"
                      Label="Наименование города" />
    }
    else if (Entity is BuildingModel building)
    {
        <MudTextField T="string"
                      @bind-Value="@building.Address"
                      @bind-Value:after="@(() => OnEntityChanged.InvokeAsync())"
                      Immediate="true"
                      Label="Адрес" />

        <!-- Карта для редактирования координат здания -->
        <MudPaper Class="ma-4"
                  Outlined="true">
            <YandexMaps @bind-Coordinates="@building.Coordinates"
                        CoordinatesWithAddressChanged="@((coordinatesWithAddress) => OnCoordinatesWithAddressChanged(building, coordinatesWithAddress))"
                        Width="100%"
                        ReadOnly="false"
                        For="@(() => building.Coordinates)"
                        Class="ma-n4 rounded-b overflow-hidden"
                        Height="400px" />
        </MudPaper>
    }
    else if (Entity is FloorModel floor)
    {
        <MudNumericField @bind-Value="@floor.Number"
                         @bind-Value:after="@(() => OnEntityChanged.InvokeAsync())"
                         Immediate="true"
                         Label="Этаж" />
    }
    else if (Entity is RoomModel room)
    {
        <MudTextField @bind-Value="@room.Name"
                      @bind-Value:after="@(() => OnEntityChanged.InvokeAsync())"
                      Immediate="true"
                      Variant="Variant.Outlined"
                      Margin="Margin.Dense"
                      Placeholder="Наименование комнаты" />

        <!-- Компонент управления камерами -->
        <CameraListComponent Cameras="@room.Cameras"
                             OnCameraAdded="@((camera) => OnCameraAdded.InvokeAsync(camera))"
                             OnCameraRemoved="@((camera) => OnCameraRemoved.InvokeAsync(camera))"
                             SearchFunc="@SearchCamerasFunc" />
    }
    else if (Entity is FreezerModel freezer)
    {
        <MudTextField @bind-Value="freezer.Name"
                      Immediate="true"
                      Label="Название холодильника" />

        <div style="border: 1px solid var(--mud-palette-divider); border-radius: 4px;">
            <SensorListComponent Sensors="freezer.Sensors" />
            <SensorFormComponent Sensors="freezer.Sensors" />
        </div>
    }
</div>

@code {
    [Parameter] public object? Entity { get; set; }
    [Parameter] public EventCallback<CamModel> OnCameraAdded { get; set; }
    [Parameter] public EventCallback<CamModel> OnCameraRemoved { get; set; }
    [Parameter] public Func<string, CancellationToken, Task<IEnumerable<CamModel>>>? SearchCamerasFunc { get; set; }
    [Parameter] public EventCallback OnEntityChanged { get; set; }

    private async Task OnCoordinatesWithAddressChanged(BuildingModel building, Components.YandexMaps.CoordinatesWithAddress? coordinatesWithAddress)
    {
        if (coordinatesWithAddress is null) return;
        if (coordinatesWithAddress.Address is null) return;

        building.Address = coordinatesWithAddress.Address;
        await OnEntityChanged.InvokeAsync();
    }
}
