@using System.Globalization

<div class="pdf-export-container">
    <!-- Заголовок -->
    <div class="pdf-export-header">
        <h1 class="pdf-export-title">Дашборд инцидентов и датчиков</h1>
        <p class="pdf-export-date">Дата экспорта: @DateTime.Now.ToString("dd.MM.yyyy HH:mm", CultureInfo.GetCultureInfo("ru-RU"))</p>
    </div>

    <!-- Фильтры -->
    <div class="pdf-export-filters">
        <h3>Примененные фильтры</h3>
        <div class="pdf-export-filters-grid">
            <div><strong>Период:</strong> @Filter.DateFrom.ToString("dd.MM.yyyy", CultureInfo.GetCultureInfo("ru-RU")) - @Filter.DateTo.ToString("dd.MM.yyyy", CultureInfo.GetCultureInfo("ru-RU"))</div>
            @if (!string.IsNullOrEmpty(Filter.CityName))
            {
                <div><strong>Город:</strong> @Filter.CityName</div>
            }
            @if (!string.IsNullOrEmpty(Filter.BuildingName))
            {
                <div><strong>Здание:</strong> @Filter.BuildingName</div>
            }
            @if (!string.IsNullOrEmpty(Filter.FloorName))
            {
                <div><strong>Этаж:</strong> @Filter.FloorName</div>
            }
            @if (!string.IsNullOrEmpty(Filter.RoomName))
            {
                <div><strong>Помещение:</strong> @Filter.RoomName</div>
            }
            @if (!string.IsNullOrEmpty(Filter.DeviceName))
            {
                <div><strong>Устройство:</strong> @Filter.DeviceName</div>
            }
        </div>
    </div>

    <!-- Графики -->
    <div class="pdf-charts-container">
        @if (ChartImages?.Any() == true)
        {
            <div class="pdf-charts-grid">
                @foreach (var chart in ChartImages)
                {
                    <img src="@chart.ImageData"
                         alt="@chart.Title"
                         class="pdf-chart-image" />
                }
            </div>
        }
        else
        {
            <div class="no-data">
                <p>Нет данных для отображения графиков</p>
            </div>
        }
    </div>

    <!-- Подвал -->
    <div class="pdf-export-footer">
        <p>Отчет сгенерирован системой Multimonitor • @DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss", CultureInfo.GetCultureInfo("ru-RU"))</p>
    </div>
</div>

@code {
    [Parameter] public FilterInfo Filter { get; set; } = new();
    [Parameter] public List<ChartImageInfo> ChartImages { get; set; } = new();

    public class FilterInfo
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string? CityName { get; set; }
        public string? BuildingName { get; set; }
        public string? FloorName { get; set; }
        public string? RoomName { get; set; }
        public string? DeviceName { get; set; }
    }

    public class ChartImageInfo
    {
        public string Title { get; set; } = string.Empty;
        public string ImageData { get; set; } = string.Empty;
    }
}
