using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Authentication.Account.LoginForceChangePassword;

public static class UserLoginChangePasswordUseCase
{
    public record Command(string Username, string CurrentPassword, string NewPassword) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;

        public Response()
        {
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound,
        WrongPassword,
        NewPasswordEqualsOld,
        UserLockedout,
        ForceChangePasswordNotSet
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Username).NotEmpty();
            RuleFor(c => c.CurrentPassword).NotEmpty();
            RuleFor(c => c.NewPassword).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager,
                       LogInDomainService loginDomainService)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var user = await _userRepository.FindByUsernameAsync(request.Username, cancellationToken);

            if (user is null)
            {
                return new Response(Result.UserNotFound);
            }

            if (PasswordHasher.VerifyHashedPassword(user.Password, request.CurrentPassword) is PasswordVerificationResult.Failed)
            {
                return new Response(Result.WrongPassword);
            }

            if (request.NewPassword == request.CurrentPassword)
            {
                return new Response(Result.NewPasswordEqualsOld);
            }

            if (user.LockoutEnabled)
            {
                return new Response(Result.UserLockedout);
            }

            if (!user.ForcePasswordChange)
            {
                return new Response(Result.ForceChangePasswordNotSet);
            }

            var hashedPassword = PasswordHasher.HashPassword(request.NewPassword);
            user.ChangePassword(hashedPassword);

            user.ResetForcePasswordChange();

            await _userRepository.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync();

            return new Response();
        }
    }
}