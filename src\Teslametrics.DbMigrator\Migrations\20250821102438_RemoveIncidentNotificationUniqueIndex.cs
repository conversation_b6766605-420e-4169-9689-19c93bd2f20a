﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    /// <inheritdoc />
    public partial class RemoveIncidentNotificationUniqueIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_incident_notifications_incident_id_user_id",
                table: "incident_notifications");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_incident_notifications_incident_id_user_id",
                table: "incident_notifications",
                columns: new[] { "incident_id", "user_id" },
                unique: true);
        }
    }
}
