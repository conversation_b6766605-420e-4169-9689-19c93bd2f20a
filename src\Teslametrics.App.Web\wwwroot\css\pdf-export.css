/* PDF Export Styles */
/* Стили для оптимизации отображения в PDF */

/* Базовые стили для PDF */
* {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
}

html, body {
    background: #ffffff !important;
    margin: 0;
    padding: 0;
}

/* Стили для таблицы */
.pdf-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 11px !important;
    margin-top: 10px !important;
    background: white !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.pdf-table thead tr {
    background-color: #1976d2 !important;
    color: white !important;
}

.pdf-table th {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
    font-weight: bold !important;
    background-color: #1976d2 !important;
    color: white !important;
    vertical-align: middle !important;
}

.pdf-table td {
    border: 1px solid #ddd !important;
    padding: 6px !important;
    background: white !important;
    vertical-align: middle !important;
    word-wrap: break-word !important;
}

.pdf-table tbody tr:nth-child(even) {
    background-color: #f9f9f9 !important;
}

.pdf-table tbody tr:nth-child(odd) {
    background-color: white !important;
}

.pdf-table tbody tr:nth-child(even) td {
    background-color: #f9f9f9 !important;
}

.pdf-table tbody tr:nth-child(odd) td {
    background-color: white !important;
}

/* Стили для статуса и иконок */
.status-icon {
    display: inline-block;
    font-weight: bold;
    font-size: 14px;
}

.status-icon.resolved {
    color: #4caf50 !important;
}

.status-icon.unresolved {
    color: #f44336 !important;
}

.incident-type {
    display: flex;
    align-items: center;
    gap: 5px;
}

.incident-icon {
    font-size: 14px;
}

.incident-type.resolved .incident-icon {
    opacity: 0.7;
}

.incident-type.unresolved .incident-icon {
    opacity: 1;
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.pdf-export-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    padding: 20px !important;
    background: white !important;
    color: #333 !important;
}

.pdf-export-header {
    text-align: center !important;
    margin-bottom: 30px !important;
    padding-bottom: 15px !important;
    border-bottom: 2px solid #1976d2 !important;
}

.pdf-export-title {
    margin: 0 !important;
    font-size: 24px !important;
    font-weight: bold !important;
    color: #1976d2 !important;
}

.pdf-export-date {
    margin: 5px 0 !important;
    font-size: 14px !important;
    color: #666 !important;
}

.pdf-export-filters {
    margin-bottom: 20px !important;
    padding: 15px !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    background-color: #f8f9fa !important;
    page-break-inside: avoid !important;
}

.pdf-export-filters h3 {
    margin: 0 0 10px 0 !important;
    font-size: 16px !important;
    color: #1976d2 !important;
}

.pdf-export-filters-grid {
    line-height: 1.5 !important;
}

.pdf-export-filters-grid div {
    margin-bottom: 5px !important;
}

.pdf-export-footer {
    margin-top: 30px;
    text-align: center;
    font-size: 10px;
    padding-top: 15px;
    page-break-inside: avoid;
}

/* Стили для графиков в PDF */
.pdf-charts-container {
    margin-top: 20px !important;
    page-break-inside: avoid !important;
}

.pdf-charts-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
}

.pdf-chart-item {
    page-break-inside: avoid !important;
    margin-bottom: 15px !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    padding: 10px !important;
    background: white !important;
}

.pdf-chart-title {
    margin: 0 0 10px 0 !important;
    font-size: 14px !important;
    font-weight: bold !important;
    color: #1976d2 !important;
    text-align: center !important;
    border-bottom: 1px solid #eee !important;
    padding-bottom: 5px !important;
}

.pdf-chart-image-container {
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

.pdf-chart-image {
    max-width: 100% !important;
    height: auto !important;
    border: none !important;
    display: block !important;
    margin: 0 auto !important;
    background: white !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Адаптация для одного графика на странице */
.pdf-charts-grid .pdf-chart-item:only-child {
    grid-column: 1 / -1 !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

/* Стили для легенды в PDF */
.pdf-chart-legend {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 12px !important;
    margin-bottom: 8px !important;
}

.pdf-chart-legend::before {
    content: " " !important;
    display: block !important;
    width: 10px !important;
    height: 10px !important;
    border-radius: 5px !important;
}

.pdf-chart-legend.current_period::before {
    background: #004854 !important;
}

.pdf-chart-legend.previous_period::before {
    background: #33C7F8 !important;
}

.pdf-chart-legend.reference_values::before {
    background: #FFD85C !important;
}

.pdf-chart-legend.door_opened::before {
    background: #004854 !important;
}

.pdf-chart-legend.average_time::before {
    background: #33C7F8 !important;
}

/* Стили для печати и PDF */
@media print {
    .pdf-export-container {
        padding: 10px;
    }

    .pdf-export-table {
        font-size: 10px;
    }

    .pdf-export-filters-grid {
        font-size: 11px;
    }

    .pdf-charts-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .pdf-chart-item {
        margin-bottom: 10px !important;
        padding: 8px !important;
    }

    .pdf-chart-title {
        font-size: 12px !important;
    }

    /* Предотвращаем разрыв строк таблицы */
    .pdf-export-table tr {
        page-break-inside: avoid;
    }

    /* Заголовок таблицы на каждой странице */
    .pdf-export-table thead {
        display: table-header-group;
    }

    .pdf-export-table tbody {
        display: table-row-group;
    }
}
