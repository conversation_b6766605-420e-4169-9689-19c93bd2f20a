<MudPaper Elevation="0"
          Outlined="true">
    <MudStack Row="true"
              Class="pa-4">
        <MudLink OnClick="() => OnCitySelected.InvokeAsync(null)">Список городов</MudLink>

        @if (SelectedCity is not null)
        {
            <MudText Color="Color.Surface"
                     Class="divider">/</MudText>
            <MudLink OnClick="() => OnBuildingSelected.InvokeAsync(null)"
                     Disabled="SelectedBuilding is null">
                @(string.IsNullOrEmpty(SelectedCity.Name) ? "Нет названия города" : SelectedCity.Name)
            </MudLink>
        }

        @if (SelectedBuilding is not null)
        {
            <MudText Color="Color.Surface"
                     Class="divider">/</MudText>
            <MudLink OnClick="() => OnFloorSelected.InvokeAsync(null)"
                     Disabled="SelectedFloor is null">
                @(string.IsNullOrEmpty(SelectedBuilding.Address) ? "Нет адреса" : SelectedBuilding.Address)
            </MudLink>
        }

        @if (SelectedFloor is not null)
        {
            <MudText Color="Color.Surface"
                     Class="divider">/</MudText>
            <MudLink OnClick="() => OnRoomSelected.InvokeAsync(null)"
                     Disabled="SelectedRoom is null">
                @SelectedFloor.Number
            </MudLink>
        }

        @if (SelectedRoom is not null)
        {
            <MudText Color="Color.Surface"
                     Class="divider">/</MudText>
            <MudLink OnClick="() => OnFreezerSelected.InvokeAsync(null)"
                     Disabled="SelectedFreezer is null">
                @(string.IsNullOrEmpty(SelectedRoom.Name) ? "Нет наименования комнаты" : SelectedRoom.Name)
            </MudLink>
        }

        @if (SelectedFreezer is not null)
        {
            <MudText Color="Color.Surface"
                     Class="divider">/</MudText>
            <MudLink Disabled>@SelectedFreezer.Name</MudLink>
        }
    </MudStack>
</MudPaper>