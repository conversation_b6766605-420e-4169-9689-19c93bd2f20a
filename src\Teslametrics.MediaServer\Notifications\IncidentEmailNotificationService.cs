using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Options;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Domain.Incidents;
using Teslametrics.MediaServer.Orleans.Wirenboard;
using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Notifications;

/// <summary>
/// Сервис для отправки email уведомлений об инцидентах
/// </summary>
public class IncidentEmailNotificationService : IIncidentEmailNotificationService
{
    private readonly IUserRepository _userRepository;
    private readonly IEmailSender _emailSender;
    private readonly ILogger<IncidentEmailNotificationService> _logger;
    private readonly WebAppSettings _webAppSettings;

    public IncidentEmailNotificationService(
        IUserRepository userRepository,
        IEmailSender emailSender,
        ILogger<IncidentEmailNotificationService> logger,
        IOptions<WebAppSettings> webAppSettings)
    {
        _userRepository = userRepository;
        _emailSender = emailSender;
        _logger = logger;
        _webAppSettings = webAppSettings.Value;
    }

    public async Task SendIncidentNotificationAsync(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo = null, CancellationToken cancellationToken = default)
    {
        await SendEmailNotificationAsync(incident, sensorTopicInfo, isResolved: false, cancellationToken);
    }

    public async Task SendIncidentResolvedNotificationAsync(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo = null, CancellationToken cancellationToken = default)
    {
        await SendEmailNotificationAsync(incident, sensorTopicInfo, isResolved: true, cancellationToken);
    }

    private async Task SendEmailNotificationAsync(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo, bool isResolved, CancellationToken cancellationToken = default)
    {
        try
        {
            // Получаем пользователей с включенными email уведомлениями
            var usersWithEmailNotifications = await _userRepository.GetUsersWithEmailNotificationsAsync(cancellationToken);

            if (usersWithEmailNotifications.Count == 0)
            {
                return;
            }

            var subject = GenerateEmailSubject(incident, isResolved);
            var body = GenerateEmailBody(incident, sensorTopicInfo, isResolved, _webAppSettings.BaseUrl);

            // Отправляем email каждому пользователю
            var emailTasks = usersWithEmailNotifications.Select(async user =>
            {
                try
                {
                    await _emailSender.SendAsync(user.Email, subject, body, cancellationToken);
                    _logger.LogInformation("Email notification sent to user {UserId} ({Email}) for incident {IncidentId} (resolved: {IsResolved})",
                        user.Id, user.Email, incident.Id, isResolved);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send email notification to user {UserId} ({Email}) for incident {IncidentId} (resolved: {IsResolved})",
                        user.Id, user.Email, incident.Id, isResolved);
                }
            });

            await Task.WhenAll(emailTasks);

            _logger.LogInformation("Email notifications processing completed for incident {IncidentId} (resolved: {IsResolved}). Sent to {UserCount} users",
                incident.Id, isResolved, usersWithEmailNotifications.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process email notifications for incident {IncidentId} (resolved: {IsResolved})", incident.Id, isResolved);
        }
    }

    private static string GenerateEmailSubject(IncidentAggregate incident, bool isResolved = false)
    {
        var incidentTypeName = GetIncidentTypeDisplayName(incident.IncidentType);
        var location = GenerateLocationString(incident);
        var locationPart = !string.IsNullOrEmpty(location) ? $" - {location}" : "";
        var statusPart = isResolved ? "Устранено" : "Происшествие";
        return $"{statusPart}: {incidentTypeName}{locationPart}";
    }

    private static string GenerateLocationString(IncidentAggregate incident)
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(incident.City))
            parts.Add(incident.City);

        if (!string.IsNullOrEmpty(incident.Building))
            parts.Add(incident.Building);

        if (incident.Floor > 0)
            parts.Add($"Floor {incident.Floor}");

        if (!string.IsNullOrEmpty(incident.Room))
            parts.Add(incident.Room);

        return string.Join(", ", parts);
    }

    private static string GetIncidentTypeDisplayName(IncidentType incidentType)
    {
        var field = incidentType.GetType().GetField(incidentType.ToString());
        var displayAttribute = field?.GetCustomAttribute<DisplayAttribute>();
        return displayAttribute?.Name ?? incidentType.ToString();
    }

    private static string GenerateSensorRangeInfo(SensorTopicInfo? sensorTopicInfo)
    {
        if (sensorTopicInfo?.SensorModel == null)
            return string.Empty;

        return sensorTopicInfo.SensorModel switch
        {
            TemperatureModel temp => $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Допустимый диапазон</td>
                                                <td>{temp.MinTemp}°C — {temp.MaxTemp}°C</td>
                                            </tr>",

            HumidityModel humidity => $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Допустимый диапазон</td>
                                                <td>{humidity.MinHumidity}% — {humidity.MaxHumidity}%</td>
                                            </tr>",

            DoorModel door => $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Допустимое время</td>
                                                <td>{door.AvailableOpeningTime} сек</td>
                                            </tr>",

            _ => string.Empty
        };
    }

    private static string GenerateEmailBody(IncidentAggregate incident, SensorTopicInfo? sensorTopicInfo, bool isResolved = false, string? baseUrl = null)
    {
        var createdAtLocal = incident.CreatedAt.ToLocalTime();
        var timezoneOffset = createdAtLocal.ToString("zzz");
        var resolvedAtLocal = incident.ResolvedAt?.ToLocalTime();

        return $@"
<center class=""56d1f58691ae1135darkfone""
        style=""background-color:#e5e5e5;width:100%"">
    <table role=""presentation""
           width=""100%""
           cellspacing=""0""
           cellpadding=""0""
           border=""0""
           style=""background:#f5f7fa; padding:24px 0;"">
        <tbody>
            <tr>
                <td align=""center"">
                    <table role=""presentation""
                           width=""600""
                           cellspacing=""0""
                           cellpadding=""0""
                           border=""0""
                           style=""width:600px; max-width:600px; background:#ffffff; border:1px solid #e5e7eb; border-radius:8px; overflow:hidden; font-family:Arial, Helvetica, sans-serif;"">
                        <!-- Шапка -->
                        <tbody>
                            <tr>
                                <td style=""padding:16px 20px;background: #00363F;color:#ffffff;"">
                                    <table role=""presentation""
                                           width=""100%""
                                           cellspacing=""0""
                                           cellpadding=""0""
                                           border=""0"">
                                        <tbody>
                                            <tr>
                                                <td style=""font-size:18px; font-weight:bold;"">
                                                    <span style=""margin-left:8px; vertical-align:middle;"">multimonitor</span>
                                                </td>
                                                <td align=""right""
                                                    style=""font-size:12px; color:#cbd5e1;"">
                                                    ID события: <strong style=""color:#ffffff;"">{incident.Id}</strong>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>

                            <!-- Маркер важности -->
                            <tr>
                                <td style=""padding:12px 20px; border-bottom:1px solid #e5e7eb;"">
                                    <span style=""font-size:14px;color:#0f172a;margin-left:8px;color: {(isResolved ? "#22c55e" : "#FF1323")};"">
                                        {(isResolved ? "✓ " : "")}{GetIncidentTypeDisplayName(incident.IncidentType)}{(isResolved ? " - Устранено" : "")}
                                    </span>
                                </td>
                            </tr>

                            <!-- Резюме -->
                            <tr>
                                <td style=""padding:16px 20px;"">
                                    <table role=""presentation""
                                           width=""100%""
                                           cellspacing=""0""
                                           cellpadding=""0""
                                           border=""0""
                                           style=""font-size:14px; color:#0f172a; line-height:1.5;"">
                                        <tbody>
                                            {(!string.IsNullOrEmpty(incident.City) ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Город</td>
                                                <td>{incident.City}</td>
                                            </tr>" : "")}
                                            {(!string.IsNullOrEmpty(incident.Building) ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Здание</td>
                                                <td>{incident.Building}</td>
                                            </tr>" : "")}
                                            {(incident.Floor > 0 ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Этаж</td>
                                                <td>{incident.Floor}</td>
                                            </tr>" : "")}
                                            {(!string.IsNullOrEmpty(incident.Room) ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Комната</td>
                                                <td>{incident.Room}</td>
                                            </tr>" : "")}
                                            {(!string.IsNullOrEmpty(incident.Device) ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Устройство</td>
                                                <td>{incident.Device}</td>
                                            </tr>" : "")}
                                            {(sensorTopicInfo?.SensorName != null ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Датчик</td>
                                                <td>{sensorTopicInfo.SensorName}</td>
                                            </tr>" : (!string.IsNullOrEmpty(incident.Topic) ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Датчик</td>
                                                <td>{incident.Topic}</td>
                                            </tr>" : ""))}
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Время обнаружения</td>
                                                <td>{createdAtLocal:dd.MM.yyyy HH:mm:ss} (UTC{timezoneOffset})</td>
                                            </tr>
                                            {(isResolved && resolvedAtLocal.HasValue ? $@"
                                            <tr>
                                                <td width=""40%""
                                                    style=""color:#64748b;"">Время устранения</td>
                                                <td>{resolvedAtLocal.Value:dd.MM.yyyy HH:mm:ss} (UTC{resolvedAtLocal.Value:zzz})</td>
                                            </tr>" : "")}
                                            {GenerateSensorRangeInfo(sensorTopicInfo)}
                                        </tbody>
                                    </table>
                                </td>
                            </tr>

                            <!-- Кнопки -->
                            <tr>
                                <td style=""padding:0 20px 20px 20px;"">
                                    <table role=""presentation""
                                           cellspacing=""0""
                                           cellpadding=""0""
                                           border=""0"">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <a class=""button""
                                                       href=""{(!string.IsNullOrEmpty(baseUrl) ? $"{baseUrl.TrimEnd('/')}/incidents?IncidentId={incident.Id}" : "#")}""
                                                       target=""_blank""
                                                       style=""background:#334155; color:#ffffff; padding:10px 14px; border-radius:6px; font-size:14px; font-weight:bold; text-decoration:none;"">К происшествию</a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>

                            <!-- Футер -->
                            <tr>
                                <td style=""padding:14px 20px; background:#f1f5f9; color:#475569; font-size:12px; border-top:1px solid #e5e7eb;"">
                                    Получено от multimonitor.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</center>";
    }
}
