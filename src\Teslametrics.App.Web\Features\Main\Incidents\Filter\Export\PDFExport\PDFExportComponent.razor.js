class PDFExporter {
	constructor() {
		this.html2pdfReady = null;
	}

	async ensureHtml2Pdf() {
		if (window.html2pdf) return;
		if (this.html2pdfReady) return this.html2pdfReady;

		this.html2pdfReady = new Promise((resolve, reject) => {
			const script = document.createElement("script");
			script.src =
				"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js";
			script.onload = () => resolve();
			script.onerror = () => reject(new Error("Failed to load html2pdf.js"));
			document.head.appendChild(script);
		});

		return this.html2pdfReady;
	}

	async fetchCssText(url) {
		const resp = await fetch(url, { credentials: "include" });
		if (!resp.ok) throw new Error(`CSS load failed: ${url} (${resp.status})`);
		return await resp.text();
	}

	async exportToPDF(
		html,
		{
			cssUrl = "/css/pdf-export.css",
			filenamePrefix = "incidents_report",
			orientation = "landscape",
		} = {}
	) {
		try {
			await this.ensureHtml2Pdf();

			// 1) Собираем DOM-контейнер
			const host = document.createElement("div");
			host.style.position = "fixed";
			host.style.left = "-10000px"; // за пределами экрана
			host.style.top = "0";
			host.style.width = "210mm"; // A4 ширина (под ландшафт подгоняет html2pdf)
			host.style.zIndex = "-1";
			host.setAttribute("data-pdf-host", "true");

			// 2) Инлайн CSS (устраняет проблемы с применением внешних стилей)
			let inlinedCss = "";
			try {
				inlinedCss = await this.fetchCssText(cssUrl);
				console.log("CSS loaded successfully, length:", inlinedCss.length);
			} catch (e) {
				console.warn("CSS loading failed:", e.message);
			}
			const styleTag = document.createElement("style");
			styleTag.setAttribute("data-inlined-pdf-css", "true");
			styleTag.textContent = `* { -webkit-print-color-adjust: exact; print-color-adjust: exact; }html, body { background: #fff !important; }${inlinedCss}`;

			// 3) Вставляем HTML как DOM
			const container = document.createElement("div");
			container.innerHTML = html;

			// Вклеиваем base для корректных относительных путей к картинкам/шрифтам (если нужно)
			const base = document.createElement("base");
			base.href = document.baseURI;

			// 4) Сборка в host и добавление в документ
			host.appendChild(base);
			host.appendChild(styleTag);
			host.appendChild(container);
			document.body.appendChild(host);

			// 5) Ждём загрузки шрифтов и картинок
			try {
				if (document.fonts?.ready) await document.fonts.ready;
			} catch {
				/* ничего */
			}
			await this.waitForImages(container);

			// 6) Настройки html2pdf/html2canvas/jsPDF
			const options = {
				margin: [10, 10, 10, 10],
				filename: `${filenamePrefix}_${
					new Date().toISOString().split("T")[0]
				}.pdf`,
				image: { type: "jpeg", quality: 0.98 },
				html2canvas: {
					scale: 2, // чётче
					useCORS: true, // тянем изображения/шрифты с CORS
					allowTaint: true,
					letterRendering: true,
					imageTimeout: 5000,
					backgroundColor: "rgba(240, 242, 248, 1)",
					logging: true, // отключаем логи html2canvas
					removeContainer: true, // удаляем контейнер после рендера
					foreignObjectRendering: false, // отключаем SVG рендеринг для лучшей совместимости
					onclone: (element) => {
						const svgElements = Array.from(element.querySelectorAll("svg"));
						svgElements.forEach((s) => {
							s.setAttribute("x", 1);
							s.setAttribute("y", 1);
							s.setAttribute("width", 22);
							s.setAttribute("height", 22);
						});
					},
				},
				jsPDF: {
					unit: "mm",
					format: "a4",
					orientation,
				},
				pagebreak: { mode: ["css", "legacy"] }, // учитывает .html2pdf__page-break
			};

			// 7) Генерация
			await window.html2pdf().set(options).from(container).save();

			// 8) Уборка
			host.remove();

			return { success: true };
		} catch (error) {
			console.error("Error generating PDF:", error);
			return { success: false, error: error.message };
		}
	}

	async waitForImages(root) {
		const imgs = Array.from(root.querySelectorAll("img")).filter(
			(img) => !img.complete || img.naturalWidth === 0
		);
		if (imgs.length === 0) return;

		await Promise.all(
			imgs.map(
				(img) =>
					new Promise((resolve) => {
						const done = () => resolve();
						img.addEventListener("load", done, { once: true });
						img.addEventListener("error", done, { once: true });
						// на случай, если событие уже было
						if (img.complete) resolve();
					})
			)
		);
	}

	async collectAllCss() {
		const chunks = [];

		// собираем все <style>
		document.querySelectorAll("style").forEach((style) => {
			chunks.push(style.textContent);
		});

		// собираем все <link rel="stylesheet">, вытаскиваем их текст
		const links = Array.from(document.querySelectorAll("link[rel=stylesheet]"));
		for (const link of links) {
			try {
				const abs = new URL(link.href, document.baseURI).href;
				const resp = await fetch(abs, { credentials: "include" });
				if (resp.ok) {
					chunks.push(await resp.text());
				} else {
					console.warn("CSS fetch failed:", abs, resp.status);
				}
			} catch (e) {
				console.warn("CSS fetch error:", e);
			}
		}

		return chunks.join("\n");
	}
}

// Экспорт для Blazor
const __pdfExporter = new PDFExporter();

export async function exportIncidentsToPDF(html, opts) {
	return await __pdfExporter.exportToPDF(html, opts);
}
