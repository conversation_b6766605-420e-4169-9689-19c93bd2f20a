using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Home.IncidentTypes;

public partial class IncidentTypesComponent
{
    // --- Render gate state ---
    private bool _allowRender = true;           // дать первый рендер
    private bool _prevHasData;
    private string _prevLegendKey = string.Empty;
    private bool _pendingBuildAfterRender;      // строить график после рендера

    private Dictionary<IncidentType, int> _incidentTypes => _response?.Incidents.ToDictionary(x => x.IncidentType, x => x.Count) ?? [];

    public record ChartData(Teslametrics.Shared.IncidentType ChartType, string Name, int Count);

    private IJSObjectReference? _jsModule;
    private DotNetObjectReference<IncidentTypesComponent>? _objRef;
    private GetIncidentTypeCountUseCase.Response? _response = null;

    #region [Injectables]
    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;
    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter]
    public DateTimeOffset DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTimeOffset DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    #endregion

    #region [JSInvokable]
    [JSInvokable]
    public async Task NavigateToIncident(int type)
    {
        var hasAccess = await CheckIfPermissionGranted(AppPermissions.Main.Incidents.Read.GetEnumPermissionString());
        if (!hasAccess) return;

        var query = new Dictionary<string, object?>
        {
            ["DateFrom"] = DateFrom.ToDateTimeOffsetString(),
            ["DateTo"] = DateTo.ToDateTimeOffsetString(),
            ["IncidentType"] = type,
        };

        var uri = _navigationManager.GetUriWithQueryParameters("/incidents", query);
        _navigationManager.NavigateTo(uri);
    }
    #endregion

    #region [LifeCycle]
    protected override void OnInitialized()
    {
        _objRef = DotNetObjectReference.Create(this);
        base.OnInitialized();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();

        var hasData = HasData(_response);
        var legendKey = LegendKey(_incidentTypes.Keys);   // подпишите под ваши поля

        // Разрешаем рендер только когда это действительно нужно:
        // 1) меняется пустота данных (переход график <-> заглушка)
        // 2) меняется состав легенды (если легенда у вас в Razor)
        _allowRender = _allowRender              // первый рендер
                       || hasData != _prevHasData
                       || legendKey != _prevLegendKey;

        _prevHasData = hasData;
        _prevLegendKey = legendKey;

        // Сам график обновляем сразу, без рендера Razor
        // (DOM не меняется), а если надо показать заглушку —
        // строить график не нужно.
        if (_jsModule != null && hasData)
        {
            _pendingBuildAfterRender = true;
        }
        else
        {
            // если сейчас пусто, но контейнер мог быть отрисован раньше —
            // дадим Razor перерисоваться и убрать его
            _pendingBuildAfterRender = false;
        }

        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Home/IncidentTypes/IncidentTypesComponent.razor.js");

                // если после первого рендера у нас есть данные — построим график
                if (_prevHasData) await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                Snackbar.Add("Не удалось загрузить скрипт для построения графика",
                    MudBlazor.Severity.Error);
            }
        }

        // на случай, если вы решите строить график строго после рендера дом-узла:
        if (_pendingBuildAfterRender && _prevHasData && _jsModule != null)
        {
            _pendingBuildAfterRender = false;
            await BuildChartsAsync();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override bool ShouldRender()
    {
        // одноразово разрешаем текущий рендер и сбрасываем флаг
        var r = _allowRender;
        _allowRender = false;
        return r;
    }
    #endregion

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        try
        {
            var chartsData = _response?.Incidents.Select(d => new ChartData(d.IncidentType, d.IncidentType.GetName()!, d.Count)).ToList();
            await _jsModule.InvokeVoidAsync("renderIncidentTypes", chartsData, _objRef);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading charts data");
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    // Mock data methods - these would be replaced with actual API calls
    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetIncidentTypeCountUseCase.Query(DateFrom, DateTo, null, null, null, null, null));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении типов происшествий во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetIncidentTypeCountUseCase.Result.Success:
                break;
            case GetIncidentTypeCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении типов происшествий", MudBlazor.Severity.Error);
                break;
            case GetIncidentTypeCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentTypesComponent), nameof(GetIncidentTypeCountUseCase));
                Snackbar.Add($"Не удалось получить типы происшествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentTypesComponent), nameof(GetIncidentTypeCountUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить типы происшествий из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private async Task<bool> CheckIfPermissionGranted(string appPermissionString)
    {
        var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var policyRequirementResource = new PolicyRequirementResource(null, null);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            appPermissionString // The policy name
        );

        return authorizationResult.Succeeded;
    }

    private static bool HasData(GetIncidentTypeCountUseCase.Response? resp)
        => resp?.Incidents is { Count: > 0 };

    // Сигнатура состава легенды.
    private static string LegendKey(IEnumerable<IncidentType> incidentTypes)
    {
        if (incidentTypes is null || !incidentTypes.Any()) return string.Empty;

        var keys = incidentTypes.OrderBy(s => s);
        return string.Join('|', keys);
    }
}
