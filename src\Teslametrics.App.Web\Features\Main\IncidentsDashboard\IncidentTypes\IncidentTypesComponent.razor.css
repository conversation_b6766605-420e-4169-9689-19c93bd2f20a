::deep #incident-types-chart-paper {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
}

.no_data {
    min-height: 250px;
}

.legends {
    height: -webkit-fill-available;
    justify-content: flex-end;
}

::deep .annotation rect {
    stroke: var(--color-stroke-light) !important;
    fill: var(--color-bg-1) !important;
}

::deep .annotation text {
    fill: var(--color-neutral-40) !important;
}

::deep div#incident-types-chart,
::deep div#incident-types-chart div,
::deep div#incident-types-chart svg {
    overflow: visible;
}

::deep.tooltip {
    position: absolute;
    font-family: Inter, sans-serif;
    pointer-events: none;
    z-index: 1000;
    white-space: nowrap;
    font-family: Inter;
    font-weight: 500;
    font-size: 10px;
    line-height: 120%;
    padding: 2px;
    border: 1px solid var(	--color-stroke-stroke);
}

::deep .content {
    color: var(--color-neutral-40);
}