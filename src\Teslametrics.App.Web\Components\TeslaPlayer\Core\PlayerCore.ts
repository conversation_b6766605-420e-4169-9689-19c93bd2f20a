import type { VideoChunk, ISignalRFeed } from "./SignalRFeed.types.js";
import type { IMediaPipeline } from "./MediaPipeline.types.js";
import type {
	IPlayerCore,
	ModeArgs,
	PlayerCoreBusEvents,
} from "./PlayerCore.types.js";
import type { Handler, Emitter } from "mitt";
import type { IPluginBase } from "../Plugins/PluginBase.types.js";
import type { Unsub } from "./index.types.js";

import mitt from "mitt";
import { PlayerCoreEvent } from "./PlayerCore.types.js";
import { ViewMode } from "./modes.types.js";
import { FeedState } from "./SignalRFeed.types.js";
/**
 * Параметры, необходимые для создания экземпляра {@link PlayerCore}.
 */
export interface PlayerCoreOptions extends ModeArgs {
	/**
	 * GUID или иной идентификатор камеры, поток которой нужно воспроизводить.
	 */
	cameraId: string;

	/**
	 * Режим воспроизведения.
	 *
	 * - `'live'` — трансляция в реальном времени;
	 * - `'point'` — воспроизведение с точки {@link PlayerCoreOptions.start};
	 * - `'range'` — воспроизведение от {@link PlayerCoreOptions.start} до {@link PlayerCoreOptions.end}.
	 *
	 * @defaultValue `'live'`
	 */
	mode?: ViewMode;

	/**
	 * Автоматически начать воспроизведение сразу после инициализации.
	 *
	 * @defaultValue `true`
	 */
	autoplay?: boolean;
}

/**
 * Ядро видеоплеера.
 *
 * Создаёт связку:
 * - {@link SignalRFeed} — получение медиасегментов по SignalR;
 * - {@link MediaPipeline} — буферизация и воспроизведение через MSE.
 *
 * Все события проксируются в событийную шину {@link PlayerCore.bus}.
 */
export class PlayerCore implements IPlayerCore {
	private readonly feed: ISignalRFeed;
	private readonly pipeline: IMediaPipeline;
	private readonly plugins = new Map<string, IPluginBase>();

	private _mode: ViewMode;

	readonly bus: Emitter<PlayerCoreBusEvents>;

	public get mode(): ViewMode {
		return this._mode;
	}

	constructor(
		feed: ISignalRFeed,
		pipeline: IMediaPipeline,
		initialMode: ViewMode
	) {
		this.feed = feed;
		this.pipeline = pipeline;

		this._mode = initialMode;

		this.bus = mitt<PlayerCoreBusEvents>();

		// notify initial mode
		this.bus.emit(PlayerCoreEvent.ModeChanged, this._mode);

		this._wireFeedEvents();
		this._wirePipelineEvents();
	}

	/** Выполняет seek к заданному абсолютному времени. */
	async seekAbs(time: Date): Promise<void> {
		if (this.feed.state === FeedState.Stopped)
			throw new Error("Player is disposed");

		this._mode = ViewMode.VOD;
		this.bus.emit(PlayerCoreEvent.ModeChanged, this._mode);
		this.bus.emit(PlayerCoreEvent.SeekAbs, time);

		try {
			// Ensure connection is established
			if (this.feed.state !== FeedState.Ready) {
				await this.feed.start();
			} else {
				this.feed.pause();
			}
			this.pipeline.clearBuffer();
			await (this.feed as any).seekTo(time);
		} catch (err) {
			this.bus.emit(
				PlayerCoreEvent.Error,
				err instanceof Error ? err : new Error(String(err))
			);
			throw err;
		}
	}

	/** Перемещается на live-край потока. */
	async seekLive(): Promise<void> {
		if (this.feed.state === FeedState.Stopped)
			throw new Error("Player is disposed");

		this._mode = ViewMode.Live;
		this.bus.emit(PlayerCoreEvent.ModeChanged, this._mode);

		try {
			// Ensure connection is established
			if (this.feed.state !== FeedState.Ready) {
				await this.feed.seekToLive();
			} else {
				this.feed.pause();
			}
			this.pipeline.seekLive();
			this.bus.emit(PlayerCoreEvent.SeekLive);
		} catch (err) {
			this.bus.emit(
				PlayerCoreEvent.Error,
				err instanceof Error ? err : new Error(String(err))
			);
		}
	}

	/** Приостанавливает получение потока и воспроизведение. */
	pause(): void {
		try {
			this.feed.pause();
			this.bus.emit(PlayerCoreEvent.Paused);
		} catch (err) {
			this.bus.emit(
				PlayerCoreEvent.Error,
				err instanceof Error ? err : new Error(String(err))
			);
		}
	}

	/** Возобновляет получение потока и воспроизведение. */
	async resume(): Promise<void> {
		try {
			// если ещё не подключено, стартуем
			if (this.feed.state !== FeedState.Ready) {
				await this.feed.start();
			} else {
				this.feed.resume();
			}
			this.bus.emit(PlayerCoreEvent.Resumed);
		} catch (err) {
			this.bus.emit(
				PlayerCoreEvent.Error,
				err instanceof Error ? err : new Error(String(err))
			);
		}
	}

	/** Переключает режим воспроизведения. */
	async setMode(
		newMode: ViewMode,
		args?: { start?: Date; end?: Date }
	): Promise<void> {
		switch (newMode) {
			case ViewMode.Live:
				await this.seekLive();
				break;
			case ViewMode.VOD:
				if (!args?.start) throw new Error("Missing start for point mode");
				await this.seekAbs(args.start);
				break;
			case ViewMode.Range:
				if (!args?.start || !args?.end)
					throw new Error("Missing start/end for range mode");

				this._mode = ViewMode.Range;
				this.bus.emit(PlayerCoreEvent.ModeChanged, this._mode);
				this.bus.emit(PlayerCoreEvent.SeekRange, args);
				try {
					// Ensure connection
					if (this.feed.state !== FeedState.Ready) {
						await this.feed.start();
					} else {
						this.feed.pause();
					}
					this.pipeline.clearBuffer();
					await (this.feed as any).seekToRange(args.start, args.end);
				} catch (err: any) {
					this.bus.emit(
						PlayerCoreEvent.Error,
						err instanceof Error ? err : new Error(String(err))
					);
					throw err;
				}
				break;
			default:
				throw new Error(`Unsupported mode: ${newMode}`);
		}
	}

	dispose(): void {
		this.plugins.forEach((p) => p.destroy());
		this.plugins.clear();

		this.feed.dispose();
		this.pipeline.dispose();
		this.bus.emit(PlayerCoreEvent.Dispose);
	}

	addPlugin(plugin: IPluginBase): void {
		if (this.plugins.has(plugin.name)) {
			console.warn(
				`[PlayerCore] Плагин '${plugin.name}' уже существует, заменяем`
			);
			this.removePlugin(plugin.name);
		}

		this.plugins.set(plugin.name, plugin);
		plugin.init(this as IPlayerCore);
	}

	subscribe<T extends PlayerCoreEvent>(
		event: T,
		handler: Handler<PlayerCoreBusEvents[T]>
	): Unsub {
		this.bus.on(event, handler);
		return () => this.bus.off(event, handler);
	}

	removePlugin(name: string): void {
		const p = this.plugins.get(name);
		if (p) p.destroy();
		this.plugins.delete(name);
	}

	private _wireFeedEvents(): void {
		this.feed.onChunk(({ data, ts }) => {
			this.pipeline.push(data, ts);
			this.bus.emit(PlayerCoreEvent.Chunk, { data, ts } as VideoChunk);
		});
		this.feed.onError((err) => this.bus.emit(PlayerCoreEvent.Error, err));
		this.feed.onState((s) => this.bus.emit(PlayerCoreEvent.State, s));
	}

	private _wirePipelineEvents(): void {
		this.pipeline.ev.on("timeupdate", (t) => {
			if (t instanceof Date) this.bus.emit(PlayerCoreEvent.TimeUpdate, t);
		});
		this.pipeline.ev.on("buffer", (range) =>
			this.bus.emit(
				PlayerCoreEvent.BufferRange,
				range as { start: number; end: number }
			)
		);
		this.pipeline.ev.on("buffer-size", (data) =>
			this.bus.emit(
				PlayerCoreEvent.BufferSize,
				data as {
					size: number;
					threshold: number;
					maxSize: number;
					isCritical: boolean;
				}
			)
		);
		this.pipeline.ev.on("error", (err) =>
			this.bus.emit(
				PlayerCoreEvent.Error,
				err instanceof Error ? err : new Error(String(err))
			)
		);
	}
}
