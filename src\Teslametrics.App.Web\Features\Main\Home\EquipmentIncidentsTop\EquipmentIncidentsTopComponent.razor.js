let incidentsChartInitialized = false;
let customTooltip = null;

const INCIDENT_TYPE_NAMES = {
	Temperature: "Температура",
	Door: "Дверь",
	Humidity: "Влажность",
	Leak: "Протечка",
	Power: "Питание",
	WirenboardDisconnected: "Wirenboard отключен",
};

const layout = {
	margin: { t: 20, r: 0, b: 40, l: 0 },
	barmode: "stack",
	paper_bgcolor: "transparent",
	// 2) внутренняя область построения (ось‑ось‑сетка)
	plot_bgcolor: "transparent",
	bargap: 0.6,
	automargin: true,
	xaxis: {
		type: "category",
		automargin: true,
		tickmode: "array",
		tickvals: [], // что реально на оси (уникальные ID)
		ticktext: [], // что показываем вместо ID
		gridcolor: "#B8C2CC",
		showspikes: false,
	},
	yaxis: {
		automargin: true,
		range: [0, 10],
		nticks: 5,
		gridcolor: "#B8C2CC",
		showspikes: false,
		tickformat: "d", // или '.0f'
	},
	hovermode: "x unified",
	showlegend: false,
};

const chartId = "equipment-incidents-top-chart";

function waitForPlotly() {
	return new Promise((resolve, reject) => {
		if (typeof Plotly !== "undefined") {
			resolve();
			return;
		}
		let tries = 0;
		(function check() {
			if (typeof Plotly !== "undefined") return resolve();
			if (++tries > 100) return reject(new Error("Plotly not loaded"));
			setTimeout(check, 100);
		})();
	});
}

/**
 * Создает кастомную HTML подсказку
 */
function createCustomTooltip() {
	if (customTooltip) return customTooltip;

	const tmpl = document.getElementById("equipmentIncidentsTopTooltipTemplate");
	customTooltip = tmpl.content.firstElementChild.cloneNode(true);
	document.body.appendChild(customTooltip);
	return customTooltip;
}

function createCustomTooltipIncident() {
	const tmpl = document.getElementById(
		"equipmentIncidentsTopTooltipIncidentTemplate"
	);
	return tmpl.content.firstElementChild.cloneNode(true);
}

/**
 * Показывает кастомную подсказку
 * @param {deviceId:string, name:string, counts: {Temperature: number, Door: number, Humidity: number, Leak: number, Power: number, WirenboardDisconnected: number}} data  // Значения ошибок устройства
 */
function showCustomTooltip(position, data) {
	/** @type {HTMLElement} */
	const tooltip = createCustomTooltip();
	/** @type {HTMLElement} */
	const incidentTypeTpl = createCustomTooltipIncident();

	const headerElement = tooltip.querySelector(".header");
	headerElement.querySelector(".header-value").textContent = Object.values(
		data.counts
	).reduce((d, a) => d + a, 0);

	const valueTextElement = tooltip.querySelector(".incidents");
	valueTextElement.innerHTML = "";

	Object.keys(INCIDENT_TYPE_NAMES)
		// 1) убираем пустые/нулевые
		.filter((key) => (data.counts[key] ?? 0) > 0)
		// 2) сортируем по убыванию значения
		.sort((a, b) => (data.counts[b] ?? 0) - (data.counts[a] ?? 0))
		// 3) создаём DOM-узлы
		.forEach((d) => {
			/** @type {HTMLElement} */
			const node = incidentTypeTpl.cloneNode(true);
			node.dataset.type = d;
			node.querySelector(".text").textContent = INCIDENT_TYPE_NAMES[d];
			node.querySelector(".value").textContent = data.counts[d];
			valueTextElement.appendChild(node);
		});

	// ----- позиционирование без overflow -----
	const gap = 8; // отступ от якоря
	const padding = 8; // «поля» от краёв вьюпорта
	const vp = {
		left: 0,
		top: 0,
		right: window.innerWidth,
		bottom: window.innerHeight,
	};

	// 1) якорь: clientX/Y, зажатые в bbox
	const ax = Math.min(
		Math.max(position.clientX, position.bbox.x0),
		position.bbox.x1
	);
	const ay = Math.min(
		Math.max(position.clientY, position.bbox.y0),
		position.bbox.y1
	);

	// 2) измерим тултип (временно делаем видимым-невидимым)
	const prevVis = tooltip.style.visibility;
	const prevDisp = tooltip.style.display;
	tooltip.style.visibility = "hidden";
	if (!tooltip.offsetWidth || !tooltip.offsetHeight)
		tooltip.style.display = "block";
	const tw = tooltip.offsetWidth;
	const th = tooltip.offsetHeight;
	tooltip.style.visibility = prevVis;
	tooltip.style.display = prevDisp;

	// 3) лимиты
	const minX = vp.left + padding;
	const minY = vp.top + padding;
	const maxX = vp.right - padding - tw;
	const maxY = vp.bottom - padding - th;

	// 4) кандидаты (центрируем по оси, где уместно)
	const candidates = {
		top: { left: ax - tw / 2, top: ay - gap - th },
		right: { left: ax + gap, top: ay - th / 2 },
		bottom: { left: ax - tw / 2, top: ay + gap },
		left: { left: ax - gap - tw, top: ay - th / 2 },
	};

	const order = ["top", "right", "bottom", "left"]; // желаемый порядок можно поменять

	const overflows = ({ left, top }) =>
		left < minX || top < minY || left > maxX || top > maxY;

	// 5) выбор позиции
	let chosen = null;
	for (const dir of order) {
		const pos = candidates[dir];
		if (!overflows(pos)) {
			chosen = { ...pos, dir };
			break;
		}
	}
	if (!chosen) {
		const preferred = candidates[order[0]];
		chosen = {
			left: Math.min(Math.max(preferred.left, minX), maxX),
			top: Math.min(Math.max(preferred.top, minY), maxY),
			dir: order[0],
		};
	}

	// 6) применяем
	tooltip.style.left = `${chosen.left}px`;
	tooltip.style.top = `${chosen.top}px`;
	tooltip.style.visibility = "visible";
}

/**
 * Скрывает кастомную подсказку
 */
function hideCustomTooltip() {
	if (customTooltip) {
		customTooltip.style.visibility = "hidden";
	}
}

/**
 * Строит/обновляет график из DTO [{count, data}, ...]
 * @param {Array<{count:number, name:string}>} dto  // ISO‑дату Blazor отдаёт строкой
 */
export async function initChart(dto, objRef) {
	await waitForPlotly().catch((err) => {
		console.error(err);
		return;
	});
	if (!dto || dto.length === 0) {
		const emptyLayout = {
			paper_bgcolor: "transparent",
			plot_bgcolor: "transparent",
			xaxis: { visible: false },
			yaxis: { visible: false },
			annotations: [
				{
					text: "Нет данных для отображения",
					xref: "paper",
					yref: "paper",
					x: 0.5,
					y: 0.5,
					showarrow: false,
					font: { size: 16, color: "#888" },
				},
			],
			margin: { t: 40, r: 10, b: 40, l: 48 },
		};
		const emptyConfig = { displayModeBar: false };
		draw(chartId, null, emptyLayout, emptyConfig, null);
		return;
	}

	/* ---------- 1 трасса ---------- */
	const incidentTypes = ["Temperature", "Door", "Humidity", "Leak", "Power"];

	const deviceIds = dto.map((d) => d.deviceId);
	const deviceNames = dto.map((d) => d.name);

	const traces = incidentTypes.map((type) => ({
		type: "bar",
		x: deviceIds, // <— ID, а не name
		y: dto.map((d) => d.counts[type] || 0),
		name: INCIDENT_TYPE_NAMES[type],
		marker: {
			color: getComputedStyle(
				document.getElementById(chartId)
			).getPropertyValue(`--color-incident-type-${type}`.toLowerCase()),
		},
		customdata: deviceNames, // <— имя прибора рядом с каждой колонкой
		hoverinfo: "none", // отключаем стандартные подсказки
	}));

	/* ---------- 2. конфиг ---------- */
	const config = {
		responsive: true,
		displayModeBar: false, // убираем тулбар Plotly
		locale: "ru", // формат dd.MM HH:mm будет русским
	};

	const y = dto
		.flatMap((device) => device.counts)
		.flatMap((counts) => Object.keys(counts).flatMap((key) => counts[key]));
	const maxY = Math.ceil(Math.max(...y) * 1.2);

	layout.yaxis.range[0] = 0;
	layout.yaxis.range[1] = maxY > 0 ? maxY : 10;

	layout.xaxis.tickvals = deviceIds;
	layout.xaxis.ticktext = deviceNames;

	const gridcolor = getComputedStyle(
		document.getElementById(chartId)
	).getPropertyValue("--color-neutral-80", "#B8C2CC");

	layout.xaxis.gridcolor = gridcolor;
	layout.yaxis.gridcolor = gridcolor;

	draw(chartId, traces, layout, config, dto, objRef);
}

function desiredBarPx(n) {
	if (n === 1) return 100;
	if (n === 2) return 77;
	if (n >= 3 && n <= 5) return 50;
	return 50; // на всё остальное — 50px, можно поменять
}

function recalcBargapPx(gd, nCats) {
	const full = gd && gd._fullLayout;
	if (!full || !full._size) return; // страховка

	const plotW = full._size.w; // ширина plot-области в px
	const target = desiredBarPx(nCats);
	const mode = full.barmode || "stack";

	let bargap;
	if (mode === "group") {
		// если когда-нибудь перейдёшь на группировку
		const nBarTraces = gd.data.filter((t) => t.type === "bar").length;
		const groupGap = full.bargroupgap || 0;
		bargap = 1 - (target * nCats * nBarTraces) / (plotW * (1 - groupGap));
	} else {
		// stack / relative / overlay — по сути один бар на категорию
		bargap = 1 - (target * nCats) / plotW;
	}

	// аккуратное ограничение: 0..0.9
	bargap = Math.max(0, Math.min(0.9, bargap));
	Plotly.relayout(gd, { bargap });
}

/**
 * @param {string} id
 * @param {object} trace
 * @param {object} layout
 * @param {object} config
 * @param {Array} dto - исходные данные для подсказок
 */
function draw(id, trace, layout, config, dto = null, objRef = null) {
	var traces = !Array.isArray(trace) ? [trace] : trace;
	// ⬇️ главное изменение — работаем с промисом
	const plotPromise = incidentsChartInitialized
		? Plotly.react(id, traces, layout, config)
		: Plotly.newPlot(id, traces, layout, config);

	incidentsChartInitialized = true;

	plotPromise.then((gd) => {
		// 1) Делаем ширину бара фиксированной в пикселях
		const nCats = (gd.data?.[0]?.x || []).length;
		recalcBargapPx(gd, nCats);

		// 2) Пересчёт при ресайзе
		const onResize = () => recalcBargapPx(gd, nCats);
		// снимаем старый слушатель, если был
		if (gd.__barPxResize)
			window.removeEventListener("resize", gd.__barPxResize);
		gd.__barPxResize = onResize;
		window.addEventListener("resize", onResize);
	});

	// Добавляем обработчики событий для кастомных подсказок
	if (dto && dto.length > 0) {
		const plotDiv = document.getElementById(id);

		// Обработчик наведения
		const byId = new Map(dto.map((d) => [d.deviceId, d]));
		plotDiv.on("plotly_hover", (ev) => {
			const pt = ev.points?.[0];
			if (!pt) return;
			const row = byId.get(pt.x); // pt.x — это deviceId
			if (!row) return;
			showCustomTooltip(
				{
					clientX: ev.event.clientX,
					clientY: ev.event.clientY,
					bbox: pt.bbox,
				},
				row
			);

			setOpacity(ev, plotDiv, 0.2, "--color-stroke-light", "#383838ff");
		});

		// Обработчик ухода мыши
		plotDiv.on("plotly_unhover", function () {
			hideCustomTooltip();
			const colorFromVar = getComputedStyle(plotDiv).getPropertyValue(
				"--color-text-input-tile",
				"#383838ff"
			);
			resetOpacity(plotDiv, colorFromVar);
		});

		// Дополнительный обработчик для скрытия при движении мыши вне графика
		plotDiv.addEventListener("mouseleave", function () {
			hideCustomTooltip();
		});

		if (objRef) {
			plotDiv.on("plotly_click", (ev) => {
				const pt = ev.points?.[0];
				if (!pt) return;
				const equipmentId = pt.x; // <— это deviceId
				objRef?.invokeMethodAsync("NavigateToIncident", equipmentId);
			});
		}
	}
}

/**
 * Очищает ресурсы компонента
 */
export function cleanupIncidentsChart() {
	// Скрываем и удаляем кастомную подсказку
	if (customTooltip) {
		hideCustomTooltip();
		if (customTooltip.parentNode) {
			customTooltip.parentNode.removeChild(customTooltip);
		}
		customTooltip = null;
	}

	// Сбрасываем флаг инициализации
	incidentsChartInitialized = false;
}

function setOpacity(
	ev,
	plotDiv,
	dimOpacity = 0.3,
	dimLabelColor = "--color-stroke-light",
	normalLabelColor = "#383838ff"
) {
	if (!ev.points || ev.points.length === 0) return;

	const activeX = ev.points[0].x;

	const update = { "marker.opacity": [] };
	for (let i = 0; i < plotDiv.data.length; i++) {
		const trace = plotDiv.data[i];
		const xs = trace.x || [];
		const len = xs.length || (trace.y ? trace.y.length : 0);

		const arr = new Array(len);
		for (let j = 0; j < len; j++) {
			const xVal = xs[j];
			const hasValue = trace.y && Number.isFinite(trace.y[j]);
			arr[j] = hasValue ? (xVal === activeX ? 1 : dimOpacity) : 0;
		}
		update["marker.opacity"][i] = arr;
	}

	Plotly.restyle(plotDiv, update);

	// меняем подписи оси
	const allX = plotDiv.data[0].x;

	/** @type {string} */
	let color = "";
	const colorFromVar = getComputedStyle(ev.event.target).getPropertyValue(
		dimLabelColor
	);
	if (!colorFromVar) {
		const rgb = hexToRgb(normalLabelColor);
		color = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${dimOpacity})`;
	} else {
		color = colorFromVar;
	}

	const activeIndex = allX.indexOf(activeX);

	Plotly.relayout(plotDiv, { "xaxis.tickfont.color": color });
	plotDiv.querySelectorAll(".xaxislayer-above > g.xtick > text")[
		activeIndex
	].style.fill = normalLabelColor;
}

function resetOpacity(plotDiv, normalLabelColor) {
	// вернуть бары
	const reset = { "marker.opacity": [] };
	for (let i = 0; i < plotDiv.data.length; i++) {
		const trace = plotDiv.data[i];
		const len = (trace.x && trace.x.length) || (trace.y ? trace.y.length : 0);
		reset["marker.opacity"][i] = new Array(len).fill(1);
	}
	Plotly.restyle(plotDiv, reset);

	// вернуть подписи
	Plotly.relayout(plotDiv, { "xaxis.tickfont.color": normalLabelColor });
}

// Если не смогли получить цвет через variable - применим opacity к нормал. Да сложно. Но?
function hexToRgb(hex) {
	let r = 0,
		g = 0,
		b = 0;
	if (hex.startsWith("#")) {
		hex = hex.slice(1);
	}
	if (hex.length === 3) {
		hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
	}
	r = parseInt(hex.substring(0, 2), 16);
	g = parseInt(hex.substring(2, 4), 16);
	b = parseInt(hex.substring(4, 6), 16);
	return { r, g, b };
}
