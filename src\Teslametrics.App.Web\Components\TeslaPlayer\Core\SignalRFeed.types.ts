import type { Handler } from "mitt";
import type { Unsub } from "./index.types.js";

/**
 * Тип для представления ошибок.
 */
export type SignalRError = InstanceType<typeof Error>;

/**
 * Возможные состояния соединения с SignalR.
 */
export enum FeedState {
	Idle = "idle",
	Connecting = "connecting",
	Ready = "ready",
	Paused = "paused",
	Stopped = "stopped",
}

/**
 * Бинарный фрагмент видео.
 *
 * Используется в событиях потока видео.
 */
export interface VideoChunk {
	data: Uint8Array;
	/**
	 * Метка времени получения/отправки фрагмента.
	 */
	ts?: Date;
}

/**
 * Тонкая обёртка над `@microsoft/signalr` + mitt:
 *
 * - устанавливает HubConnection;
 * - пересылает бинарные сегменты наружу событием `'chunk'`;
 * - публикует изменение состояния (`'state'`) и ошибки (`'error'`);
 * - поддерживает паузу/резюмирование без закрытия соединения.
 *
 * Используется внутри {@link PlayerCore}.
 */
export interface ISignalRFeed {
	/** Текущее состояние подключения. */
	readonly state: FeedState;

	/** Выполняет seek к указанной дате (VOD/Replay). */
	seekTo(time: Date): Promise<void>;

	/** Перемещает на live-край (последнее доступное). */
	seekToLive(): Promise<void>;

	/** Диапазонный seek, пока не поддерживается */
	//seekToRange?(start: Date, end: Date): Promise<void>;

	/**
	 * Открывает соединение и запрашивает поток.
	 * Становится `ready`, когда сервер подтвердил начало стрима.
	 */
	start(): Promise<void>; // Todo: проверить, а нужен ли start()? По сути у нас есть SeekTo(abs?) и SeekToLive(). В будушем будет ещё и seekToRange(). Так зачем нужен start()?

	/**
	 * Просит сервер временно прекратить отправку данных (без разрыва WebSocket).
	 */
	pause(): void;

	/**
	 * Возобновляет приостановленный поток после {@link pause}.
	 */
	resume(): void;

	/**
	 * Полностью выключает поток и освобождает ресурсы HubConnection.
	 */
	dispose(): Promise<void>;

	/**
	 * Подписка на получение фрагментов видео.
	 *
	 * @param handler Обработчик фрагмента.
	 * @returns Функция отписки.
	 */
	onChunk(handler: Handler<VideoChunk>): Unsub;

	/**
	 * Подписка на ошибки соединения/потока.
	 *
	 * @param handler Обработчик ошибки.
	 * @returns Функция отписки.
	 */
	onError(handler: Handler<SignalRError>): Unsub;

	/**
	 * Подписка на изменения состояния подключения.
	 *
	 * @param handler Обработчик состояния.
	 * @returns Функция отписки.
	 */
	onState(handler: Handler<FeedState>): Unsub;
}
