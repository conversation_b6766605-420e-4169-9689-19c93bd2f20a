using Teslametrics.App.Web.Components;
using Microsoft.AspNetCore.Components;
using Blazor.Diagrams;
using Microsoft.AspNetCore.Components.Forms;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Diagram;

public partial class FloorPlanDiagramComponent
{
    [Parameter]
    public FloorModel? Floor { get; set; }
    [Parameter]
    public RoomModel? SelectedRoom { get; set; }
    [Parameter, EditorRequired]
    public BlazorDiagram Diagram { get; set; } = null!;
    [Parameter]
    public bool IsEditInProgress { get; set; }
    [Parameter]
    public EventCallback<IBrowserFile> OnFileChanged { get; set; }
    [Parameter]
    public EventCallback OnImageDeleted { get; set; }
    [Parameter]
    public EventCallback OnRoomZoneEditStarted { get; set; }
    [Parameter]
    public EventCallback OnRoomZoneEditEnded { get; set; }

    private async Task DeleteImage()
    {
        if (OnImageDeleted.HasDelegate)
        {
            await OnImageDeleted.InvokeAsync();
        }
    }

    private async Task StartRoomZoneEdit()
    {
        if (OnRoomZoneEditStarted.HasDelegate)
        {
            await OnRoomZoneEditStarted.InvokeAsync();
        }
    }

    private async Task EndRoomZoneEdit()
    {
        if (OnRoomZoneEditEnded.HasDelegate)
        {
            await OnRoomZoneEditEnded.InvokeAsync();
        }
    }
}
