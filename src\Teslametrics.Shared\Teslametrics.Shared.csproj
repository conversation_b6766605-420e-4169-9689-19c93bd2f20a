﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Cryptography.KeyDerivation" Version="9.0.6" />
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.1.2" />
    <PackageReference Include="Ulid" Version="1.3.4" />
    <PackageReference Include="Dapper.SqlBuilder" Version="2.1.66" />
  </ItemGroup>
  
</Project>
