using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Components.TeslaPlayer;

public partial class PlayPausePluginComponent
{
    [CascadingParameter(Name = "Player")]
    private IPlayer? _player { get; set; }

    [Inject]
    private IJSRuntime Js { get; set; } = null!;

    private IJSObjectReference? _jsModule;

    protected override void OnParametersSet()
    {

    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender && _player is not null)
        {
            try
            {
                _jsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./js/compiled/Components/TeslaPlayer/PlayPausePluginComponent.razor-module.js");
                // получаем ctor как IJSObjectReference
                await _player.AddPluginAsync("PlayPausePlugin", _jsModule);
            }
            catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
            {
            }
            catch (Exception ex)
            {
            }
        }
    }
}
