using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.EquipmentIncidentsTop;

public static class GetEquipmentIncidentsTopUseCase
{
    public record Query(int Limit, DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }
        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents)
        {
            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        public record Incident(Guid DeviceId, string Name, IReadOnlyDictionary<IncidentType, int> Counts); // Чтобы сразу в будушем не менять Dto лучше собрать в Dictionary. Если Против - ну ок. Мне не принципиально.
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        CityNotFound,
        BuildingNotFound,
        FloorNotFound,
        RoomNotFound,
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(x => x.DateTo)
                .GreaterThan(x => x.DateFrom)
                .WithMessage("DateTo must be greater than DateFrom");
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.DeviceId)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.IncidentType)
                .Select("COUNT(*) AS Count")
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { DateFrom = request.DateFrom.UtcDateTime })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { DateTo = request.DateTo.UtcDateTime })
                .Where(Db.Incidents.Props.IncidentType, ":IncidentType", SqlOperator.NotEquals, new { IncidentType = IncidentType.WirenboardDisconnected.ToString() })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .GroupBy(Db.Incidents.Props.DeviceId, Db.Incidents.Props.Device, Db.Incidents.Props.IncidentType)
                .OrderBy("COUNT(*)", OrderDirection.Descending)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidents = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            // Группируем по устройствам и создаем словарь с количеством по типам инцидентов
            var groupedIncidents = incidents
                .GroupBy(i => new { i.DeviceId, i.Device })
                .Select(g => new Response.Incident(
                    g.Key.DeviceId,
                    g.Key.Device,
                    g.ToDictionary(
                        x => x.IncidentType,
                        x => (int)x.Count
                    )
                ))
                .OrderByDescending(i => i.Counts.Values.Sum())
                .Take(request.Limit)
                .ToList();

            return new Response(groupedIncidents);
        }
    }

    public record IncidentModel(Guid DeviceId, string Device, IncidentType IncidentType, long Count);
}