using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public partial class TwoFaSetupInstructionComponent
{
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Small, NoHeader = true, CloseButton = true, BackdropClick = false };
    private int _index;

    #region [Injectables]
    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public EventCallback<bool> IsVisibleChanged { get; set; }
    #endregion

    #region [Actions]
    private Task CancelAsync()
    {
        _index = 0;

        IsVisible = false;
        return IsVisibleChanged.InvokeAsync(IsVisible);
    }

    private void HandleSwipeEnd(SwipeEventArgs args)
    {
        if (args.SwipeDirection == SwipeDirection.LeftToRight && _index < 2)
        {
            _index++;
        }

        if (args.SwipeDirection == SwipeDirection.RightToLeft && _index > 0)
        {
            _index--;
        }
    }

    #endregion

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _dialogOptions = new()
            {
                CloseOnEscapeKey = true,
                FullWidth = true,
                FullScreen = true,
                NoHeader = true
            };
        }

        base.OnInitialized();
    }
}
