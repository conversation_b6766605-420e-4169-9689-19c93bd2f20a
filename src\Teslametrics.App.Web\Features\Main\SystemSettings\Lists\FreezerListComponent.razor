@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
<MudList T="FreezerModel"
         SelectedValue="SelectedFreezer"
         SelectedValueChanged="SelectedValueChanged">
    <MudListSubheader Class="d-flex">
        Список холодильников в комнате
        <MudSpacer />
        <MudButton OnClick="@(() => AddFreezer())">Добавить холодильник</MudButton>
    </MudListSubheader>

    @foreach (var freezer in Freezers)
    {
        <MudListItem Value="@freezer"
                     Icon="@TeslaIcons.Devices.Fridge"
                     @key="@freezer">
            <ChildContent>
                <div class="d-flex flex-row align-center gap-3">
                    <div class="d-flex flex-column px-6 py-2">
                        <MudText Typo="Typo.body1">
                            Холодильник: @freezer.Name
                        </MudText>
                    </div>
                    <MudSpacer />
                    <MudTooltip Text="Удалить холодильник">
                        <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                       Color="Color.Warning"
                                       OnClick="@(() => RemoveFreezer(freezer))" />
                    </MudTooltip>
                </div>
            </ChildContent>
        </MudListItem>
    }

    @if (Freezers.Count == 0)
    {
        <div class="pa-4">
            <NoItemsFoundComponent HasItems="false" />
        </div>
    }
</MudList>
