import type { ModeConfig } from "./Core/modes.types.js";
import type { IPluginBase, PluginFactory } from "./Plugins/PluginBase.types.js";
import type { IPlayerCore } from "./Core/PlayerCore.types.js";

import { ViewMode } from "./Core/modes.types.js";
import { SignalRFeed } from "./Core/SignalRFeed.js";
import { MediaPipeline } from "./Core/MediaPipeline.js";
import { PlayerCore } from "./Core/PlayerCore.js";
import { MODES } from "./Core/modes.js";
import { PlayerCoreEvent } from "./Core/PlayerCore.types.js";

const players = new Map<string, PlayerCore>();
const statusCallbacks = new Map();

interface pluginRef {
	getFactory(): PluginFactory;
}

export function initializePlayer(
	videoRef: HTMLVideoElement,
	cameraId: string,
	mode: ViewMode, // 'live' | 'point' | 'range'
	startIso: string | null, // string or null
	endIso: string | null, // string or null
	pluginFactories?: Array<PluginFactory> // [ core => new SomePlugin(core), ... ]  video: HTMLVideoElement,
): IPlayerCore {
	// 1) Если плеер с этим ID есть — остановить его
	const old = players.get(cameraId);
	if (old) {
		old.bus.all.clear();
		old.dispose();
		players.delete(cameraId);
	}

	// 2) Собираем конфиг
	let cfg: ModeConfig;
	switch (mode) {
		case ViewMode.Live:
			cfg = MODES["live"]();
			break;
		case ViewMode.VOD:
			if (!startIso) throw new Error("Missing start for point mode");
			cfg = MODES["point"]({
				start: new Date(startIso),
			});
			break;
		case ViewMode.Range:
			if (!startIso) throw new Error("Missing start for point mode");
			if (!endIso) throw new Error("Missing end for range mode");
			cfg = MODES["range"]({
				start: new Date(startIso),
				end: new Date(endIso),
			});
			break;
		default:
			throw new Error(`Unsupported mode: ${mode}`);
	}

	// 3) Создаём feed и pipeline
	const feed = new SignalRFeed(cameraId, {
		wsPath: cfg.wsPath,
		autoReconnect: cfg.autoreconnect,
	});
	const pipeline = new MediaPipeline(videoRef, cfg.mime, {
		keep: cfg.keep,
	});

	// 4) Создаём ядро
	const core = new PlayerCore(feed, pipeline, mode);

	// 5) Регистрируем плагины
	if (pluginFactories) {
		for (const factory of pluginFactories) {
			const plugin: IPluginBase = new factory(core);
			core.addPlugin(plugin);
		}
	}

	players.set(cameraId, core);

	// 6) Подписываемся на события для Blazor (оставляем как было)
	const callback = statusCallbacks.get(cameraId);
	if (callback) {
		core.bus.on(PlayerCoreEvent.State, (s) =>
			callback.invokeMethodAsync("OnCameraStatusChanged", s)
		);
		core.bus.on(PlayerCoreEvent.Error, (e) =>
			callback.invokeMethodAsync("OnCameraError", e.message)
		);
		core.bus.on(PlayerCoreEvent.TimeUpdate, (t) =>
			callback.invokeMethodAsync("OnTimeUpdate", t.toISOString())
		);
	}

	// 7) Стартуем плеер в нужном режиме
	(async () => {
		try {
			switch (mode) {
				case ViewMode.Live:
					await core.seekLive();
					break;
				case ViewMode.VOD:
					if (!startIso) throw new Error("Missing start for VOD mode");
					await core.seekAbs(new Date(startIso));
					break;
				case ViewMode.Range:
					if (!startIso) throw new Error("Missing start for range mode");
					if (!endIso) throw new Error("Missing end for range mode");
					await core.setMode(ViewMode.Range, {
						start: new Date(startIso),
						end: new Date(endIso),
					});
					break;
				default:
					throw new Error(`Unsupported mode: ${mode}`);
			}
		} catch (err) {
			console.error("Failed to start player:", err);
		}
	})();

	return core;
}

// обёртки pause/resume/live/stop
export function addPlugin(cameraId: string, refAny: pluginRef) {
	const core = players.get(cameraId);
	if (!core) return;

	const plugin: IPluginBase = new (refAny.getFactory())(core);

	core.addPlugin(plugin);
}
export const pauseStream = (id: string) => players.get(id)?.pause();
export const resumeStream = (id: string) => players.get(id)?.resume();
export const seekToLive = (id: string) => players.get(id)?.seekLive();
export const stopStream = (id: string) => {
	const p = players.get(id);
	if (p) {
		p.bus.all.clear();
		p.dispose();
		players.delete(id);
		statusCallbacks.delete(id);
	}
};
