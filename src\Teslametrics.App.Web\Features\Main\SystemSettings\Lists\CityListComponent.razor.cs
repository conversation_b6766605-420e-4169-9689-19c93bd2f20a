using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Lists;

public partial class CityListComponent
{
    [Parameter] public List<CityModel> Cities { get; set; } = [];
    [Parameter] public CityModel? SelectedCity { get; set; }
    [Parameter] public EventCallback<CityModel?> OnCitySelected { get; set; }
    [Parameter] public Func<Task>? OnCityAdded { get; set; }
    [Parameter] public Func<CityModel, Task>? OnCityRemoved { get; set; }

    private async Task AddCity()
    {
        if (OnCityAdded != null)
            await OnCityAdded();
    }

    private async Task RemoveCity(CityModel city)
    {
        if (OnCityRemoved != null)
            await OnCityRemoved(city);
    }

    private Task SelectedValueChanged(CityModel building)
    {
        if (OnCitySelected.HasDelegate)
            return OnCitySelected.InvokeAsync(building);
        return Task.CompletedTask;
    }
}
