﻿@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<MudPaper Elevation="0"
          Outlined="true"
          Class="pa-4 mud-height-full"
          id="temperature-chart-paper">
    <div class="d-flex align-center gap-3">
        <MudText Typo="Typo.subtitle1"
                 Class="mb-4">Динамика температуры</MudText>
        <MudSpacer />
        <MudText Class="legend current_period">За период</MudText>
        <MudText Class="legend previous_period">За прошлый период</MudText>
        <MudText Class="legend reference_values">Допустимые значения</MudText>
    </div>
    <div id="temperature-chart"
         style="height: 300px;"></div>
</MudPaper>