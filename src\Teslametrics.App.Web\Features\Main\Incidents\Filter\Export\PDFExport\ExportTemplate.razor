﻿<div class="pdf-export-container">
    <!-- Заголовок отчета -->
    <div class="pdf-export-header">
        <h1 class="pdf-export-title">Отчет по происшествиям</h1>
        <p class="pdf-export-date">Дата выгрузки: @DateTime.Now.ToShortDateString()</p>
    </div>

    <!-- Информация о фильтрах -->
    <div class="pdf-export-filters">
        <h3>Параметры отчета:</h3>
        <div class="pdf-export-filters-grid">
            <div><strong>Период:</strong> @Filter.DateFrom - @Filter.DateTo</div>
            <div><strong>Фильтры:</strong></div>
            @if (Filter.CityName is not null)
            {
                <div><strong>Город:</strong>@(Filter.CityName)</div>
            }
            @if (Filter.BuildingName is not null)
            {
                <div><strong>Здание:</strong>@(Filter.BuildingName)</div>
            }
            @if (Filter.FloorName is not null)
            {
                <div><strong>Этаж:</strong>@(Filter.FloorName)</div>
            }
            @if (Filter.RoomName is not null)
            {
                <div><strong>Помещение:</strong>@(Filter.RoomName)</div>
            }
            @if (Filter.DeviceName is not null)
            {
                <div><strong>Оборудование:</strong>@(Filter.DeviceName)</div>
            }
            @if (Filter.IncidentType.HasValue)
            {
                <div><strong>Тип происшествия:</strong>@(Filter.IncidentType.Value.GetName())</div>
            }
            @if (Filter.IsResolved.HasValue)
            {
                <div><strong>Статус:</strong>@(Filter.IsResolved.Value ? "Завершено" : "Активное")</div>
            }
        </div>
    </div>
    <!-- Таблица с данными -->
    <MudDataGrid T="Incident"
                 Items="@Incidents"
                 SortMode="@SortMode.None"
                 Groupable="false"
                 MultiSelection="false"
                 Striped="true"
                 Class="mud-height-full br_12">
        <Columns>
            <TemplateColumn Title="Статус">
                <CellTemplate>
                    <div class="d-flex justify-start">
                        <MudIcon Icon="@(context.Item.IsResolved? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
                                 Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
                    </div>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.City"
                            Title="Город" />
            <PropertyColumn Property="x => x.Address"
                            Title="Здание" />
            <PropertyColumn Property="x => x.IncidentType == Teslametrics.Shared.IncidentType.WirenboardDisconnected ? string.Empty : x.Floor.ToString()"
                            Title="Этаж" />
            <PropertyColumn Property="x => x.Room"
                            Title="Помещение" />
            <PropertyColumn Property="x => x.Device"
                            Title="Оборудование" />
            <PropertyColumn Property="x => x.Date.Date.ToShortDateString()"
                            Title="Дата" />
            <PropertyColumn Property="@(x => x.Date.ToString("HH:mm"))"
                            Title="Время" />
            <TemplateColumn Title="Тип происшествия">
                <CellTemplate>
                    <MudStack Row="true"
                              AlignItems="AlignItems.Center"
                              Justify="Justify.SpaceBetween"
                              Class="actions">
                        <MudIcon Icon="@GetIncidentIcon(context.Item.IncidentType)"
                                 Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
                        @context.Item.IncidentType.GetName()
                    </MudStack>
                </CellTemplate>
            </TemplateColumn>
        </Columns>
        <NoRecordsContent>
            <MudText Typo="Typo.body1"
                     Align="Align.Center"
                     Color="Color.Secondary">
                Нет данных для отображения
            </MudText>
        </NoRecordsContent>
    </MudDataGrid><!-- Подвал отчета -->
    <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
        <p>Отчет сгенерирован системой Multimonitor</p>
    </div>
</div>