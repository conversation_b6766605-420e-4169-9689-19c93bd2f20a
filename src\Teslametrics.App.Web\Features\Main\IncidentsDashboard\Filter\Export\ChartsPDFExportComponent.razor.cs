using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using MudBlazor;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.Export;

public partial class ChartsPDFExportComponent
{
    private IJSObjectReference? _jsModule;

    #region [Injectables]
    [Inject] private IJSRuntime JSRuntime { get; set; } = null!;
    [Inject] private IServiceProvider ServiceProvider { get; set; } = null!;
    [Inject] private ILoggerFactory LoggerFactory { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter] public DateTime DateFrom { get; set; } = DateTime.Today;
    [Parameter] public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter] public Guid? CityId { get; set; }
    [Parameter] public Guid? BuildingId { get; set; }
    [Parameter] public Guid? FloorId { get; set; }
    [Parameter] public Guid? RoomId { get; set; }
    [Parameter] public Guid? DeviceId { get; set; }
    #endregion

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import",
                    "./Features/Main/IncidentsDashboard/Filter/Export/ChartsPDFExportComponent.razor.js");
            }
            catch (JSDisconnectedException)
            {
                // Игнорируем ошибки отключения JavaScript
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to load charts PDF export script: {Message}", ex.Message);
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    public async Task ExportChartsAsync()
    {
        if (_jsModule == null)
        {
            Snackbar.Add("Модуль экспорта не загружен", Severity.Error);
            return;
        }

        try
        {
            await SetLoadingAsync(true);

            var filterNamesResponse = await ScopeFactory.MediatorSend(new GetFilterNamesUseCase.Query(new DateTimeOffset(DateFrom.ToUniversalTime()), new DateTimeOffset(DateTo.ToUniversalTime()), CityId, BuildingId, FloorId, RoomId, DeviceId));

            // Показываем индикатор загрузки
            Snackbar.Add("Подготовка графиков для экспорта...", Severity.Info);

            // Захватываем все графики как изображения
            var chartImagesArray = await _jsModule.InvokeAsync<ChartsPDFTemplate.ChartImageInfo[]>("captureAllCharts");

            if (chartImagesArray == null || chartImagesArray.Length == 0)
            {
                Snackbar.Add("Не удалось захватить графики", Severity.Warning);
                return;
            }

            var chartImages = chartImagesArray.ToList();

            if (!chartImages.Any())
            {
                Snackbar.Add("Нет доступных графиков для экспорта", Severity.Warning);
                return;
            }

            // Подготавливаем данные для шаблона
            var filterInfo = new ChartsPDFTemplate.FilterInfo
            {
                DateFrom = DateFrom,
                DateTo = DateTo,
                CityName = filterNamesResponse.CityName,
                BuildingName = filterNamesResponse.BuildingName,
                FloorName = filterNamesResponse.FloorName,
                RoomName = filterNamesResponse.RoomName,
                DeviceName = filterNamesResponse.DeviceName
            };

            var templateData = new Dictionary<string, object?>
            {
                { nameof(ChartsPDFTemplate.Filter), filterInfo },
                { nameof(ChartsPDFTemplate.ChartImages), chartImages }
            };

            // Рендерим шаблон в HTML
            var html = await RenderComponentToString<ChartsPDFTemplate>(templateData);

            // Экспортируем в PDF
            var result = await _jsModule.InvokeAsync<object>("captureChartsAndExportToPDF", html, new
            {
                cssUrl = "/css/pdf-export.css",
                filenamePrefix = "dashboard_charts",
                orientation = "landscape"
            });

            Snackbar.Add("PDF файл успешно сгенерирован", Severity.Success);
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибки отключения JavaScript
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error exporting charts to PDF: {Message}", ex.Message);
            Snackbar.Add("Не удалось создать PDF отчет с графиками", Severity.Error);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }

    private async Task<string> RenderComponentToString<TComponent>(Dictionary<string, object?>? parameters = null)
        where TComponent : IComponent
    {
        await using var htmlRenderer = new HtmlRenderer(ServiceProvider, LoggerFactory);

        var html = await htmlRenderer.Dispatcher.InvokeAsync(async () =>
        {
            var parameterView = ParameterView.FromDictionary(parameters ?? []);
            var output = await htmlRenderer.RenderComponentAsync<TComponent>(parameterView);
            return output.ToHtmlString();
        });

        return html;
    }

    public async ValueTask DisposeAsync()
    {
        if (_jsModule != null)
        {
            try
            {
                await _jsModule.DisposeAsync();
            }
            catch (JSDisconnectedException)
            {
                // Игнорируем ошибки отключения JavaScript
            }
        }

        GC.SuppressFinalize(this);
    }
}
