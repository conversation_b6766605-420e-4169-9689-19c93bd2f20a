{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Graylog", "Serilog.Exceptions"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "Graylog", "Args": {"hostnameOrAddress": "**************", "port": "12201", "transportType": "Udp", "facility": "teslametrics-perftest", "shortId": true, "messageIdGenerator": "Timestamp"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "Kestrel": {"Endpoints": {"HttpEndpoint": {"Url": "http://0.0.0.0:80"}}}, "ConnectionStrings": {"Default": "Host=timescaledb;Port=5432;Database=teslametrics;Username=admin;Password=****************", "Kafka": "kafka:9092"}, "Minio": {"Endpoint": "minio:9000", "AccessKey": "SaeoEjKbrbsgztECK0Hk", "SecretKey": "4Pvinse3Xd58hByV2GH9k3CRFczW0kJbiKRPepcy"}, "Wirenboard": {"BrokerAddress": "************", "Port": 1883}, "Orleans": {"Hostname": "media-server", "Endpoint": {}}, "EmailSender": {"Host": "smtp.mail.ru", "Port": 465, "UserName": "<EMAIL>", "Password": "dn8OTnnWQYhC3L97K8QV", "EnableSsl": true, "FromDisplayName": "multimonitor", "FromAddress": "<EMAIL>"}, "WebApp": {"BaseUrl": "http://***************/"}, "AllowedHosts": "*"}