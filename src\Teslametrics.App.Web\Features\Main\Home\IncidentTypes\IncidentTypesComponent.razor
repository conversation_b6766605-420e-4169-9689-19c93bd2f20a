﻿@using Teslametrics.Shared
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<div class="pa-4 mud-height-full">
	<MudText Typo="Typo.subtitle1"
			 Class="mb-4">Типы происшествий</MudText>
	@if (_response?.Incidents.Count > 0)
	{
		<MudStack Row="true"
				  Justify="Justify.SpaceBetween"
				  AlignItems="AlignItems.End"
				  Class="mud-height-full mud-width-full">
			<div class="legends d-flex align-start flex-column gap-3">
				@foreach (var item in _incidentTypes)
				{
					<LegendItemComponent IsVisible="true"
										 Type="@item.Key" />
				}
			</div>
			<div id="incident-types-chart"
				 class="mud-height-full mud-width-full"
				 style="height: 300px;"></div>
		</MudStack>
	}
	else
	{
		<div class="d-flex flex-column align-center justify-center no_data gap-2 mud-height-full">
			<div class="icon_container bg_success br_6 d-flex align-center justify-center">
				<MudIcon Icon="@TeslaIcons.Outlined.CheckCircle"
						 Style="font-size: 16px;"
						 Color="Color.Success" />
			</div>
			<MudText Typo="Typo.body1"
					 Class="color_text_placeholder">Нет происшествий за выбранный период</MudText>
		</div>
	}
</div>
<template id="incident_types_tooltip_tmpl">
	<div class="tooltip br_4 bg_1">
		<div class="content">
		</div>
	</div>
</template>