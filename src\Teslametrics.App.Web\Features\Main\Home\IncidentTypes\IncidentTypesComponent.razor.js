let customTooltip = null;

const CHART_DIV_ID = "incident-types-chart";
// сколько сдвигать: 50% толщины сектора
const SHIFT_K = 0.5; // 0.5 = 50%
const OUTSIDE_PAD_PX = 6; // необязательно: чуть-чуть вынести за край

// ────────────────────────────────────────────────────────────────────────────
// 1. Загрузка Plotly (при необходимости)
// ────────────────────────────────────────────────────────────────────────────
if (typeof Plotly === "undefined") {
	const s = document.createElement("script");
	s.src = "/js/plotly-3.0.1.min.js";
	s.defer = true;
	document.head.appendChild(s);
}

// цвета
const rootStyles = getComputedStyle(document.documentElement);

// Маппинг типов инцидентов на CSS-переменные
const INCIDENT_TYPE_MAPPING = {
	0: "temperature",
	1: "door",
	2: "humidity",
	3: "leak",
	4: "power",
	5: "wirenboard-disconnected",
};
/**
 * Создает кастомную HTML подсказку
 */
function createCustomTooltip() {
	if (customTooltip) return customTooltip;

	const tmpl = document.getElementById("incident_types_tooltip_tmpl");
	customTooltip = tmpl.content.firstElementChild.cloneNode(true);
	document.body.appendChild(customTooltip);
	return customTooltip;
}

function showCustomTooltip(positions, data) {
	/** @type {HTMLElement} */
	const tooltip = createCustomTooltip();

	const contentElement = tooltip.querySelector(".content");
	contentElement.textContent = `${data.label}: ${data.count}; ${data.percentage}%`;

	const { width: tooltipWidth } = tooltip.getBoundingClientRect();

	const left = positions.bounds.left - (tooltipWidth - positions.bbox.width);

	// 6) применяем
	tooltip.style.left = `${left}px`;
	tooltip.style.top = `${positions.bounds.top}px`;
	tooltip.style.visibility = "visible";
}

/**
 * Скрывает кастомную подсказку
 */
function hideCustomTooltip() {
	if (customTooltip) {
		customTooltip.style.visibility = "hidden";
	}
}

// Функция получения цвета из CSS-переменной
function getIncidentColor(chartType) {
	const incidentTypeName = INCIDENT_TYPE_MAPPING[chartType];
	if (!incidentTypeName) return "#CED3D3";

	const cssVarName = `--color-incident-type-${incidentTypeName.toLowerCase()}`;
	const color = rootStyles.getPropertyValue(cssVarName).trim();
	return color || "#CED3D3";
}

// ────────────────────────────────────────────────────────────────────────────
// 3. Ожидание Plotly
// ────────────────────────────────────────────────────────────────────────────
function waitForPlotly() {
	return new Promise((resolve, reject) => {
		if (typeof Plotly !== "undefined") {
			resolve();
			return;
		}
		let tries = 0;
		(function check() {
			if (typeof Plotly !== "undefined") return resolve();
			if (++tries > 100) return reject(new Error("Plotly not loaded"));
			setTimeout(check, 100);
		})();
	});
}

// ────────────────────────────────────────────────────────────────────────────
// 2. Публичная функция (adapter)
// ────────────────────────────────────────────────────────────────────────────
export async function renderIncidentTypes(rows, objRef) {
	const labels = rows.map((r) => r.name);
	const series = rows.map((r) => r.count);
	const colors = rows.map((r) => getIncidentColor(r.chartType));
	/* ---------- 0. «Нет данных» ---------- */
	if (!series || series.length === 0) {
		return;
	}

	await waitForPlotly().catch((err) => {
		console.error(err);
		return;
	});

	const total = series.reduce((sum, v) => sum + v, 0);

	// 4.1 Domain / геометрия пончика (легенда слева → x‑range начинается с 0.3)
	const pieDomain = { x: [0, 1], y: [0, 1] };
	const domainW = pieDomain.x[1] - pieDomain.x[0];
	const domainH = pieDomain.y[1] - pieDomain.y[0];
	const cx = (pieDomain.x[0] + pieDomain.x[1]) / 2;
	const cy = (pieDomain.y[0] + pieDomain.y[1]) / 2;

	const trace = {
		type: "pie",
		labels,
		values: series,
		textinfo: "none",
		hole: 0.65,
		rotation: 90, // старт в 3 часа
		direction: "clockwise",
		sort: false, // ← сохраняем порядок элементов rows
		customdata: rows.map((p) => ({
			count: p.count,
			chartType: p.chartType,
			percentage: ((p.count / total) * 100).toFixed(0),
		})), // любые данные
		marker: { colors, line: { color: "#CED3D3", width: 1 } },
		domain: pieDomain,
		hoverinfo: "none", // отключаем стандартные подсказки
	};

	// 4.2 Средние углы с учётом rotation
	const startDeg = 90 - trace.rotation; // rotation CCW
	const angles = [];
	let curDeg = startDeg;
	for (let i = 0; i < series.length; i++) {
		const segDeg = (series[i] / total) * 360;
		angles.push(curDeg - segDeg / 2);
		curDeg -= segDeg; // clockwise
	}

	// 4.3 Аннотации — учитываем соотношение сторон контейнера
	const { width: pxW, height: pxH } = document
		.getElementById(CHART_DIV_ID)
		.getBoundingClientRect();

	let outerPx = Math.min(pxW * domainW, pxH * domainH) / 2;
	const hole = trace?.hole ?? 0;

	const innerPx = outerPx * hole;
	const midPx = innerPx + (outerPx - innerPx) * 0.5; // середина кольца

	// если есть donut
	const thicknessPx = outerPx * (1 - hole);

	// целевой радиус для аннотаций: сдвигаем от mid на 50% толщины
	// можно добавить маленький паддинг наружу
	const targetRpx = Math.min(
		outerPx + OUTSIDE_PAD_PX,
		midPx + SHIFT_K * thicknessPx
	);

	// в paper-координаты (учёт не-квадратного контейнера)
	const radX = targetRpx / pxW;
	const radY = targetRpx / pxH;

	const annotations = series.map((val, idx) => {
		const pct = val / total;
		const ang = (angles[idx] * Math.PI) / 180;

		const x = cx + Math.cos(ang) * radX;
		const y = cy + Math.sin(ang) * radY;

		return {
			x,
			y,
			xref: "paper",
			yref: "paper",
			text: `${val}; ${(pct * 100).toFixed(0)}%`,
			showarrow: false,
			font: {
				family: "Inter, sans-serif",
				size: 10,
				color: "#474B4E",
				weight: 500,
				lineHeight: 1.2,
			},
			bgcolor: "red",
			bordercolor: "green",
			borderwidth: 1,
			borderpad: 2,
			xanchor: "center",
			yanchor: "middle",
		};
	});

	// 4.4 Layout
	const layout = {
		paper_bgcolor: "transparent",
		plot_bgcolor: "transparent",
		height: 300,
		margin: { t: 0, b: 0, l: 0, r: 0 },
		showlegend: false,
		font: { family: "Inter, sans-serif", size: 8 },
		annotations,
	};

	// 4.5 Рендер
	Plotly.react(CHART_DIV_ID, [trace], layout, {
		displayModeBar: false,
		responsive: true,
	}).then(() => {
		setupHoverInteractivity();
		applyCustomAnnotationStyles();
	});

	const plotDiv = document.getElementById(CHART_DIV_ID);
	if (objRef) {
		plotDiv.on("plotly_click", (data) => {
			hideCustomTooltip();
			objRef.invokeMethodAsync(
				"NavigateToIncident",
				data.points[0].customdata[0].chartType
			);
		});
	}

	plotDiv.on("plotly_hover", (ev) => {
		var annotation = plotDiv.querySelector(
			`g.annotation[data-index="${ev.points[0].i}"]`
		);
		showCustomTooltip(
			{
				bbox: annotation.getBBox(),
				bounds: annotation.getBoundingClientRect(),
			},
			{
				label: ev.points[0].label,
				...ev.points[0].customdata[0],
			}
		);

		hideAnnotations(plotDiv);
	});

	// Обработчик ухода мыши
	plotDiv.on("plotly_unhover", function () {
		hideCustomTooltip();
		showAnnotations(plotDiv);
	});

	// Дополнительный обработчик для скрытия при движении мыши вне графика
	plotDiv.addEventListener("mouseleave", function () {
		hideCustomTooltip();
		showAnnotations(plotDiv);
	});
}

function hideAnnotations(plotDiv) {
	plotDiv.querySelectorAll("g.annotation").forEach((r) => {
		r.style.visibility = "hidden";
	});
}

function showAnnotations(plotDiv) {
	plotDiv.querySelectorAll("g.annotation").forEach((r) => {
		r.style.visibility = "visible";
	});
}

// ────────────────────────────────────────────────────────────────────────────
// 5. Скруглённые углы + тень
// ────────────────────────────────────────────────────────────────────────────
function applyCustomAnnotationStyles() {
	var el = document.getElementById(CHART_DIV_ID);
	const svg = el?.querySelector(".main-svg");
	if (!svg) return;
	el.querySelectorAll("g.annotation-text-g rect").forEach((r) => {
		r.setAttribute("rx", "4");
		r.setAttribute("ry", "4");
		r.style.filter = "drop-shadow(0 2px 4px rgba(0,0,0,0.1))";
	});
	el.querySelectorAll("g.annotation-text-g text").forEach((t) => {
		t.style.fontWeight = "500";
		t.style.letterSpacing = "0.02em";
	});
}

// ────────────────────────────────────────────────────────────────────────────
// 6. Интерактивность при наведении
// ────────────────────────────────────────────────────────────────────────────
function setupHoverInteractivity() {
	const plotDiv = document.getElementById(CHART_DIV_ID);
	if (!plotDiv) return;

	// Обработчик наведения на сегмент
	plotDiv.on("plotly_hover", (data) => {
		const hoveredIndex = data.points[0].pointNumber;
		applyHoverEffect(hoveredIndex);
	});

	// Обработчик ухода курсора с сегмента
	plotDiv.on("plotly_unhover", () => {
		resetHoverEffect();
	});
}

function applyHoverEffect(hoveredIndex) {
	const svg = document.getElementById(CHART_DIV_ID)?.querySelector(".main-svg");
	if (!svg) return;

	// Получаем все сегменты диаграммы
	const pieSlices = svg.querySelectorAll("g.slice");

	pieSlices.forEach((slice, index) => {
		if (index !== hoveredIndex) {
			// Делаем неактивные сегменты полупрозрачными
			slice.style.opacity = "0.2";
		} else {
			// Активный сегмент остается непрозрачным
			slice.style.opacity = "1";
		}
	});

	// Скрываем подписи неактивных сегментов
	const annotations = svg.querySelectorAll("g.annotation-text-g");
	annotations.forEach((annotation, index) => {
		if (index !== hoveredIndex) {
			annotation.style.opacity = "0";
		} else {
			annotation.style.opacity = "1";
		}
	});
}

function resetHoverEffect() {
	const svg = document.getElementById(CHART_DIV_ID)?.querySelector(".main-svg");
	if (!svg) return;

	// Возвращаем всем сегментам полную непрозрачность
	const pieSlices = svg.querySelectorAll("g.slice");
	pieSlices.forEach((slice) => {
		slice.style.opacity = "1";
	});

	// Показываем все подписи
	const annotations = svg.querySelectorAll("g.annotation-text-g");
	annotations.forEach((annotation) => {
		annotation.style.opacity = "1";
	});
}
