using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Home.IncidentsCount;

/// <summary>
/// Представляет точку данных для графика
/// </summary>
/// <param name="Date">Дата</param>
/// <param name="Count">Количество</param>
public record ChartDataPoint(DateTime Date, int Count);

public partial class IncidentsCountComponent : IAsyncDisposable
{
    private IJSObjectReference? _jsModule;

    private GetIncidentsCountUseCase.Response? _dailyResponse = null;
    private GetIncidentsCountForDayUseCase.Response? _hourlyResponse = null;

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;

    [Parameter]
    public DateTimeOffset DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTimeOffset DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    protected override bool ShouldRender() => false;

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
        await BuildChartsAsync();
        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Home/IncidentsCount/IncidentsCountComponent.razor.js");
                await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Не удалось загрузить JavaScript модуль для компонента IncidentsCountComponent");
                Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
            }
        }
    }

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        try
        {
            var isHourlyMode = IsHourlyMode();
            var processedIncidents = isHourlyMode
                ? ConvertToChartData(PreprocessHourlyIncidentsData(_hourlyResponse?.Incidents))
                : ConvertToChartData(PreprocessDailyIncidentsData(_dailyResponse?.Incidents));

            var dateFormat = isHourlyMode ? "HH:mm" : "dd.MM";
            await _jsModule.InvokeVoidAsync("initIncidentsChart", processedIncidents, dateFormat);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при построении графика инцидентов в компоненте IncidentsCountComponent");
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    /// <summary>
    /// Определяет, нужно ли использовать режим отображения по часам
    /// </summary>
    /// <returns>true, если период находится в пределах одного дня; false - для режима по дням</returns>
    private bool IsHourlyMode()
    {
        return DateFrom.Date == DateTo.Date;
    }

    /// <summary>
    /// Заполняет пропущенные даты нулевыми значениями для обеспечения непрерывной временной шкалы на графике (режим по дням)
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <returns>Обработанный список инцидентов с заполненными пропусками</returns>
    private List<GetIncidentsCountUseCase.Response.Incident> PreprocessDailyIncidentsData(List<GetIncidentsCountUseCase.Response.Incident>? incidents)
    {
        if (incidents == null || incidents.Count == 0)
        {
            // Если нет данных, создаем пустой список с нулевыми значениями для всего диапазона дат
            return FillMissingDatesWithZeros([], DateFrom, DateTo);
        }

        return FillMissingDatesWithZeros(incidents, DateFrom, DateTo);
    }

    /// <summary>
    /// Заполняет пропущенные часы нулевыми значениями для обеспечения непрерывной временной шкалы на графике (режим по часам)
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <returns>Обработанный список инцидентов с заполненными пропусками</returns>
    private List<GetIncidentsCountForDayUseCase.Response.Incident> PreprocessHourlyIncidentsData(List<GetIncidentsCountForDayUseCase.Response.Incident>? incidents)
    {
        if (incidents == null || incidents.Count == 0)
        {
            // Если нет данных, создаем пустой список с нулевыми значениями для всего диапазона часов
            return FillMissingHoursWithZeros([], DateFrom, DateTo);
        }

        return FillMissingHoursWithZeros(incidents, DateFrom, DateTo);
    }

    /// <summary>
    /// Конвертирует данные инцидентов в формат для графика
    /// </summary>
    private static List<ChartDataPoint> ConvertToChartData(List<GetIncidentsCountUseCase.Response.Incident> incidents)
    {
        return [.. incidents.Select(i => new ChartDataPoint(i.Date, i.Count))];
    }

    /// <summary>
    /// Конвертирует данные инцидентов по часам в формат для графика
    /// </summary>
    private static List<ChartDataPoint> ConvertToChartData(List<GetIncidentsCountForDayUseCase.Response.Incident> incidents)
    {
        return [.. incidents.Select(i => new ChartDataPoint(i.Date, i.Count))];
    }

    /// <summary>
    /// Заполняет пропущенные часы в указанном диапазоне нулевыми значениями
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <param name="startDate">Начальная дата диапазона</param>
    /// <param name="endDate">Конечная дата диапазона</param>
    /// <returns>Список инцидентов с заполненными пропусками</returns>
    private static List<GetIncidentsCountForDayUseCase.Response.Incident> FillMissingHoursWithZeros(
        List<GetIncidentsCountForDayUseCase.Response.Incident> incidents,
        DateTimeOffset startDate,
        DateTimeOffset endDate)
    {
        // Создаем словарь для быстрого поиска существующих данных по часу
        var incidentsByHour = incidents.ToDictionary(i => i.Date, i => i.Count);

        var result = new List<GetIncidentsCountForDayUseCase.Response.Incident>();

        // Проходим по всем часам в диапазоне
        for (var hour = new DateTime(startDate.Year, startDate.Month, startDate.Day, startDate.Hour, 0, 0);
             hour <= endDate;
             hour = hour.AddHours(1))
        {
            var count = incidentsByHour.TryGetValue(hour, out var existingCount) ? existingCount : 0;
            result.Add(new GetIncidentsCountForDayUseCase.Response.Incident(hour, count));
        }

        return result;
    }

    /// <summary>
    /// Заполняет пропущенные даты в указанном диапазоне нулевыми значениями
    /// </summary>
    /// <param name="incidents">Исходные данные об инцидентах</param>
    /// <param name="startDate">Начальная дата диапазона</param>
    /// <param name="endDate">Конечная дата диапазона</param>
    /// <returns>Список инцидентов с заполненными пропусками</returns>
    private static List<GetIncidentsCountUseCase.Response.Incident> FillMissingDatesWithZeros(
        List<GetIncidentsCountUseCase.Response.Incident> incidents,
        DateTimeOffset startDate,
        DateTimeOffset endDate)
    {
        // Создаем словарь для быстрого поиска существующих данных по дате
        var incidentsByDate = incidents.ToDictionary(i => i.Date.Date, i => i.Count);

        var result = new List<GetIncidentsCountUseCase.Response.Incident>();

        // Проходим по всем дням в диапазоне
        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var count = incidentsByDate.TryGetValue(date, out var existingCount) ? existingCount : 0;
            result.Add(new GetIncidentsCountUseCase.Response.Incident(date, count));
        }

        return result;
    }

    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            var isHourlyMode = IsHourlyMode();

            if (isHourlyMode)
            {
                _hourlyResponse = await ScopeFactory.MediatorSend(new GetIncidentsCountForDayUseCase.Query(DateFrom, DateTo));
                _dailyResponse = null;
            }
            else
            {
                _dailyResponse = await ScopeFactory.MediatorSend(new GetIncidentsCountUseCase.Query(DateFrom, DateTo, null, null, null, null, null));
                _hourlyResponse = null;
            }
        }
        catch (Exception ex)
        {
            _dailyResponse = null;
            _hourlyResponse = null;
            Snackbar.Add("Ошибка при получении количества происшествий во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, "Ошибка при загрузке данных инцидентов в компоненте IncidentsCountComponent");
        }

        await SetLoadingAsync(false);

        // Проверяем результат в зависимости от режима
        if (IsHourlyMode())
        {
            if (_hourlyResponse is null) return;
            HandleHourlyResponse(_hourlyResponse);
        }
        else
        {
            if (_dailyResponse is null) return;
            HandleDailyResponse(_dailyResponse);
        }
    }

    private void HandleHourlyResponse(GetIncidentsCountForDayUseCase.Response response)
    {
        switch (response.Result)
        {
            case GetIncidentsCountForDayUseCase.Result.Success:
                break;
            case GetIncidentsCountForDayUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении количества происшествий по часам", MudBlazor.Severity.Error);
                break;
            case GetIncidentsCountForDayUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountForDayUseCase));
                Snackbar.Add($"Не удалось получить количество происшествий по часам из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountForDayUseCase), response.Result);
                Snackbar.Add($"Не удалось получить количество происшествий по часам из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void HandleDailyResponse(GetIncidentsCountUseCase.Response response)
    {
        switch (response.Result)
        {
            case GetIncidentsCountUseCase.Result.Success:
                break;
            case GetIncidentsCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении количества происшествий по дням", MudBlazor.Severity.Error);
                break;
            case GetIncidentsCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase));
                Snackbar.Add($"Не удалось получить количество происшествий по дням из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase), response.Result);
                Snackbar.Add($"Не удалось получить количество происшествий по дням из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            // Очищаем ресурсы JavaScript модуля
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("cleanupIncidentsChart");
                await _jsModule.DisposeAsync();
                _jsModule = null;
            }
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибки отключения SignalR
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Ошибка при очистке ресурсов компонента IncidentsCountComponent");
        }

        GC.SuppressFinalize(this);
    }
}