using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace Teslametrics.App.Web.Features.Main.Home.Filter;

public partial class FilterComponent
{
    #region [Local Filter State]
    private DateRange _dateRange => new(DateFrom, DateTo);
    private TimeSpan _timeFrom => DateFrom.TimeOfDay;
    private TimeSpan _timeTo => DateTo.TimeOfDay;
    #endregion

    #region [Parameters]
    [Parameter]
    public string Class { get; set; } = string.Empty;

    #region [Filter Parameters]
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;
    [Parameter]
    public EventCallback<DateTime> DateFromChanged { get; set; }

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public EventCallback<DateTime> DateToChanged { get; set; }
    #endregion
    #endregion

    private async Task UpdateParametersAsync<T>(params (T? value, EventCallback<T?> callback)[] updates)
    {
        var tasks = new List<Task>();

        foreach (var (value, callback) in updates)
        {
            if (callback.HasDelegate)
                tasks.Add(callback.InvokeAsync(value));
        }

        await Task.WhenAll(tasks);
        StateHasChanged();
    }

    private Task OnDateRangeChanged(DateRange dateRange)
    {
        DateFrom = dateRange.Start ?? new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, _timeFrom.Hours, _timeFrom.Minutes, 0, 0);
        if (dateRange.End is null)
        {
            DateTo = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, _timeTo.Hours, _timeTo.Minutes, 59, 999);
        }
        else
        {
            DateTo = new DateTime(dateRange.End.Value.Year, dateRange.End.Value.Month, dateRange.End.Value.Day, dateRange.End.Value.Hour, dateRange.End.Value.Minute, 59, 999);
        }

        return UpdateParametersAsync(
            (DateFrom, DateFromChanged),
            (DateTo, DateToChanged)
        );

    }
}
