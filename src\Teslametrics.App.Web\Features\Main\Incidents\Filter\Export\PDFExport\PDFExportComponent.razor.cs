using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.PDFExport;

public partial class PDFExportComponent
{
    private GetIncidentListUseCase.Response? _response;

    private IJSObjectReference? _jsModule;

    #region [Injectables]
    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;

    [Inject]
    private IServiceProvider _serviceProvider { get; set; } = null!;
    [Inject]
    private ILoggerFactory _loggerFactory { get; set; } = null!;

    #endregion

    #region [Parameters]
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);

    [Parameter]
    public Guid? CityId { get; set; } = null;

    [Parameter]
    public Guid? BuildingId { get; set; } = null;

    [Parameter]
    public Guid? FloorId { get; set; } = null;

    [Parameter]
    public Guid? RoomId { get; set; } = null;

    [Parameter]
    public Guid? FridgeId { get; set; } = null;

    [Parameter]
    public IncidentType? IncidentType { get; set; }

    [Parameter]
    public bool? IsResolved { get; set; } = null;
    #endregion

    protected override bool ShouldRender()
    {
        return false;
    }

    private async Task ExportAsync()
    {
        if (IsLoading || _jsModule is null) return;

        try
        {
            await SetLoadingAsync(true);
            // Получаем данные инцидентов
            await GetIncidentsDataAsync();

            if (_response is null || !_response.IsSuccess)
            {
                Snackbar.Add("Нет данных для экспорта", MudBlazor.Severity.Warning);
                return;
            }

            ExportTemplate.Filters filter = new(DateFrom, DateTo, _response.CityName, _response.BuildingName, _response.FloorName, _response.RoomName, _response.DeviceName, IncidentType, IsResolved);
            List<ExportTemplate.Incident> data = [.. _response.Incidents.Select(i => new ExportTemplate.Incident(i.Id, i.City, i.Address, i.Floor, i.Room, i.Device, i.Date, i.IncidentType, i.IsResolved))];

            var dictionary = new Dictionary<string, object?>
            {
                { nameof(ExportTemplate.Filter), filter },
                { nameof(ExportTemplate.Incidents), data }
            };

            // Рендерим компонент в HTML
            var html = await RenderComponentToString<ExportTemplate>(dictionary);

            // Вызываем JavaScript функцию для генерации PDF
            await _jsModule.InvokeAsync<object>("exportIncidentsToPDF", html, new
            {
                cssUrl = "/css/pdf-export.css",
                filenamePrefix = "incidents_report",
                orientation = "landscape"
            });
        }
        catch (JSDisconnectedException)
        {
            // Игнорируем ошибки отключения JavaScript
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось создать PDF отчет", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        await SetLoadingAsync(false);
    }

    private async Task GetIncidentsDataAsync()
    {
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetIncidentListUseCase.Query(
                new DateTimeOffset(DateFrom.ToUniversalTime()),
                new DateTimeOffset(DateTo.ToUniversalTime()),
                CityId,
                BuildingId,
                FloorId,
                RoomId,
                FridgeId,
                IncidentType,
                IsResolved));
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить данные инцидентов из-за ошибки на сервере. Обратитесь к администратору", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Incidents/Filter/Export/PDFExport/PDFExportComponent.razor.js");
            }
            catch (JSDisconnectedException)
            {
                // Игнорируем ошибки отключения JavaScript
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to load PDF export script: {Message}", ex.Message);
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    public async Task<string> RenderComponentToString<TComponent>(Dictionary<string, object?>? dictionary = null) where TComponent : IComponent
    {
        await using var htmlRenderer = new Microsoft.AspNetCore.Components.Web.HtmlRenderer(_serviceProvider, _loggerFactory);

        var html = await htmlRenderer.Dispatcher.InvokeAsync(async () =>
        {
            var parameters = ParameterView.FromDictionary(dictionary ?? []);
            var output = await htmlRenderer.RenderComponentAsync<TComponent>(parameters);

            return output.ToHtmlString();
        });

        return html;
    }
}
