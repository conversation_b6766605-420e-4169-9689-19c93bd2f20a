let incidentsChartInitialized = false;
let customTooltip = null;

/** @type {SVGRectElement} */
let badgeEl = null;

const barDimmColorVariable = "--bar-color-dimmed";
const barNormalColorVariable = "--bar-color";
const labelDimmColorVariable = "--label-color-dimmed";
const labelNormalColorVariable = "--label-color";

const layout = {
	// 1) сам «лист» (вся область SVG / Canvas), подсказки
	paper_bgcolor: "transparent",
	// 2) внутренняя область построения (ось‑ось‑сетка)
	plot_bgcolor: "transparent",
	margin: { t: 20, r: 10, b: 20, l: 10, pad: 18 },
	bargap: 0.6,
	automargin: true,
	xaxis: {
		type: "date",
		tickformat: "%d.%m", // ось: 21.07
		showline: false,
		gridcolor: "rgba(0,0,0,0.1)",
		ticktext: [],
		tickvals: [],
		automargin: true,
		showspikes: false,
	},
	yaxis: {
		range: [0, 10],
		tickformat: "d", // убираем запятую для целых чисел
		gridcolor: "rgba(0,0,0,0.1)",
		showline: false,
		zeroline: true,
		zerolinecolor: "rgba(0,0,0,0.2)",
		automargin: true,
		showspikes: false,
	},
	hovermode: "x unified",
	showlegend: false,
};

const chartId = "incidents-chart";

/**
 * Создает кастомную HTML подсказку
 * @returns {HTMLDivElement}
 */
function createCustomTooltip() {
	if (customTooltip) return customTooltip;

	const tmpl = document.getElementById("incident_count_tooltip_tmpl"); // По-хорошему это всё можно передавать при инициализации компонента
	/** @type {HTMLElement} */
	customTooltip = tmpl.content.firstElementChild.cloneNode(true);
	document.body.appendChild(customTooltip);
	return customTooltip;
}

function createBadge(plotDiv) {
	if (badgeEl) return;

	const tpl = document.getElementById("incident_count_tooltip_lable_tmpl");
	badgeEl = tpl.content.querySelector("rect").cloneNode(true);
	plotDiv.querySelector(".xaxislayer-above").appendChild(badgeEl);
}

/**
 * Показывает кастомную подсказку
 * @param {{left:number, top:number}} position  // ISO‑дату Blazor отдаёт строкой
 * @param {{x: string, y: number}} data // x хранит в себе дату события y - количество происшествий
 * @param {string} dateFormat // формат отображения даты: "dd.MM" для дней, "HH:mm" для часов
 */
function showCustomTooltip(position, data, dateFormat) {
	const tooltip = createCustomTooltip();
	const headerElement = tooltip.querySelector(".header");
	const valueElement = tooltip.querySelector(".value");

	// Форматируем дату в зависимости от режима
	const date = new Date(data.x);

	/** @type {string} **/
	let formattedDate;

	if (dateFormat === "HH:mm") {
		// Режим по часам - показываем дату и время
		const formatterDate = new Intl.DateTimeFormat("ru-RU", {
			weekday: "long",
			day: "numeric",
			month: "long",
		});

		const formatterTime = new Intl.DateTimeFormat("ru-RU", {
			hour: "2-digit",
			minute: "2-digit",
		});

		// начало
		const start = formatterTime.format(date);
		// конец (через 59 минут)
		const end = formatterTime.format(new Date(date.getTime() + 59 * 60 * 1000));

		formattedDate = `${formatterDate.format(date)}, ${start} - ${end}`;
	} else {
		// Режим по дням - показываем только дату
		const formatter = new Intl.DateTimeFormat("ru-RU", {
			weekday: "long",
			day: "numeric",
			month: "long",
		});

		formattedDate = formatter.format(date);
	}

	// Заполняем данные
	headerElement.textContent = formattedDate;
	valueElement.innerHTML = data.y;

	tooltip.style.left = `${position.left}px`;
	tooltip.style.top = `${position.top}px`;
	tooltip.style.visibility = "visible";
	tooltip.style.opacity = "1";
}

/**
 * Скрывает кастомную подсказку
 */
function hideCustomTooltip() {
	if (customTooltip) {
		customTooltip.style.visibility = "hidden";
		customTooltip.style.opacity = "0";
	}
}

/**
 * Строит/обновляет график из DTO [{count, data}, ...]
 * @param {Array<{count:number, date:string}>} dto  // ISO‑дату Blazor отдаёт строкой
 * @param {string} dateFormat                       // 'dd.MM' для дней, 'HH:mm' для часов
 */
export function initIncidentsChart(dto, dateFormat = "dd.MM") {
	if (!dto || dto.length === 0) {
		const emptyLayout = {
			paper_bgcolor: "transparent",
			plot_bgcolor: "transparent",
			xaxis: { visible: false },
			yaxis: { visible: false },
			annotations: [
				{
					text: "Нет данных для отображения",
					xref: "paper",
					yref: "paper",
					x: 0.5,
					y: 0.5,
					showarrow: false,
					font: { size: 16, color: "#888" },
				},
			],
			margin: { t: 40, r: 10, b: 40, l: 48 },
		};
		const emptyConfig = { displayModeBar: false };
		draw(chartId, null, emptyLayout, emptyConfig, null);
		return;
	}

	/* ---------- 1. данные ---------- */
	const x = dto.map((d) => d.date); // оставляем «сырые» ISO‑даты
	const y = dto.map((d) => d.count);

	/* ---------- 2. трасса ---------- */
	const trace = {
		type: "bar",
		x,
		y,
		name: "Происшествий",
		marker: { color: "#004854" },
		// отключаем стандартные подсказки
		hoverinfo: "none",
	};

	/* ---------- 3. конфиг ---------- */
	const config = {
		responsive: true,
		displayModeBar: false, // убираем тулбар Plotly
		locale: "ru", // формат dd.MM HH:mm будет русским
	};

	const maxValue = Math.max(...y);
	const maxY = Math.max(Math.ceil(maxValue * 1.2), maxValue + 1);

	// Динамически настраиваем ось Y для предотвращения дублирования меток
	layout.yaxis.range[0] = 0;
	layout.yaxis.range[1] = maxY > 0 ? maxY : 10;

	// Устанавливаем количество меток в зависимости от диапазона значений
	if (maxY <= 5) {
		// Для малых значений - показываем каждое целое число
		layout.yaxis.dtick = 1;
		delete layout.yaxis.nticks;
	} else if (maxY <= 10) {
		// Для средних значений - шаг 2
		layout.yaxis.dtick = 2;
		delete layout.yaxis.nticks;
	} else {
		// Для больших значений - автоматическое определение
		delete layout.yaxis.dtick;
		layout.yaxis.nticks = Math.min(Math.ceil(maxY / 2), 10);
	}
	layout.xaxis.tickvals = dto.map((d) => d.date);

	// Форматируем метки оси X в зависимости от режима
	if (dateFormat === "HH:mm") {
		// Режим по часам - показываем только четные часы в числовом формате
		layout.xaxis.ticktext = dto.map((d) => {
			const hour = new Date(d.date).getHours();
			// Показываем метку только для четных часов, для остальных - пустая строка
			return hour % 2 === 0 ? hour.toString() : "";
		});
	} else {
		// Режим по дням - показываем дату
		layout.xaxis.ticktext = dto.map((d) =>
			new Date(d.date).toLocaleDateString("ru-RU", {
				day: "2-digit",
				month: "2-digit",
			})
		);
	}

	layout.xaxis.type = "category"; // или оставить "date", но тогда шаги будут сложнее контролировать

	draw(chartId, trace, layout, config, dto, dateFormat);
}

/**
 * @param {string} id
 * @param {object} trace
 * @param {object} layout
 * @param {object} config
 * @param {Array} dto - исходные данные для подсказок
 * @param {string} dateFormat - формат отображения даты
 */
function draw(id, trace, layout, config, dto = null, dateFormat = "dd.MM") {
	var traces = trace ? [trace] : null;
	if (incidentsChartInitialized) {
		Plotly.react(id, traces, layout, config);
	} else {
		Plotly.newPlot(id, traces, layout, config);
		incidentsChartInitialized = true;
	}

	// Добавляем обработчики событий для кастомных подсказок
	if (dto && dto.length > 0) {
		const plotDiv = document.getElementById(id);

		// Обработчик наведения
		plotDiv.on("plotly_hover", function (eventData) {
			if (eventData.points && eventData.points.length > 0) {
				const point = eventData.points[0];
				const dataIndex = point.pointIndex;

				if (dataIndex >= 0 && dataIndex < dto.length) {
					const data = {
						x: dto[dataIndex].date,
						y: dto[dataIndex].count,
					};
					let left =
						eventData.points[0].bbox.x0 +
						(eventData.points[0].bbox.x1 - eventData.points[0].bbox.x0) / 2;

					let top = eventData.points[0].bbox.y0 + 10;

					showCustomTooltip({ left, top }, data, dateFormat);
				}
			}

			setOpacity(eventData, plotDiv, dateFormat);
		});

		// Обработчик ухода мыши
		plotDiv.on("plotly_unhover", function () {
			hideCustomTooltip();
			const colorFromVar = getComputedStyle(plotDiv).getPropertyValue(
				"--color-text-input-tile",
				"#383838ff"
			);
			resetOpacity(plotDiv, colorFromVar);
			hideBadge();
		});

		// Дополнительный обработчик для скрытия при движении мыши вне графика
		plotDiv.addEventListener("mouseleave", function () {
			hideCustomTooltip();
		});

		plotDiv.on("plotly_afterplot", () => {
			createBadge(plotDiv);
		});
	}
}

function showBadgeForText(textEl) {
	const bb = textEl.getBBox();
	badgeEl.setAttribute("x", bb.x - 6);
	badgeEl.setAttribute("y", bb.y - 4);
	badgeEl.setAttribute("width", bb.width + 12);
	badgeEl.setAttribute("height", bb.height + 8);
	badgeEl.setAttribute("transform", textEl.getAttribute("transform"));
	badgeEl.style.display = "";
}

function hideBadge() {
	badgeEl.style.display = "none";
}

/**
 * Очищает ресурсы компонента
 */
export function cleanupIncidentsChart() {
	// Скрываем и удаляем кастомную подсказку
	if (customTooltip) {
		hideCustomTooltip();
		if (customTooltip.parentNode) {
			customTooltip.parentNode.removeChild(customTooltip);
		}
		customTooltip = null;
	}

	// Сбрасываем флаг инициализации
	incidentsChartInitialized = false;
}

function setOpacity(ev, plotDiv, dateFormat) {
	const computedStyles = getComputedStyle(plotDiv);

	const dimBarColor = computedStyles.getPropertyValue(barDimmColorVariable);
	const defaultBarColor = computedStyles.getPropertyValue(
		barNormalColorVariable
	);
	const dimLabelColor = getComputedStyle(ev.event.target).getPropertyValue(
		labelDimmColorVariable
	);
	const normalLabelColor = getComputedStyle(ev.event.target).getPropertyValue(
		labelNormalColorVariable
	);

	if (!ev.points || ev.points.length === 0) return;

	const activeX = ev.points[0].x;

	const update = { "marker.color": [] };
	for (let i = 0; i < plotDiv.data.length; i++) {
		const trace = plotDiv.data[i];
		const xs = trace.x || [];
		const len = xs.length || (trace.y ? trace.y.length : 0);

		const arr = new Array(len);
		for (let j = 0; j < len; j++) {
			arr[j] = xs[j] === activeX ? defaultBarColor : dimBarColor;
		}
		update["marker.color"][i] = arr;
	}

	Plotly.restyle(plotDiv, update);

	// меняем подписи оси
	const allX = plotDiv.data[0].x;

	Plotly.relayout(plotDiv, { "xaxis.tickfont.color": dimLabelColor });

	const activeIndex = allX.indexOf(activeX);
	if (dateFormat === "HH:mm") {
		if (activeIndex % 2 !== 0) return;

		const el = plotDiv.querySelectorAll(".xaxislayer-above > g.xtick > text")[
			Math.floor(activeIndex / 2)
		];
		el.style.fill = normalLabelColor;
	} else {
		const el = plotDiv.querySelectorAll(".xaxislayer-above > g.xtick > text")[
			activeIndex
		];
		el.style.fill = normalLabelColor;
		showBadgeForText(el);
	}
}

function resetOpacity(plotDiv, normalLabelColor) {
	const computedStyles = getComputedStyle(plotDiv);

	const defaultBarColor = computedStyles.getPropertyValue(
		barNormalColorVariable
	);
	// вернуть бары
	const reset = { "marker.color": [] };
	for (let i = 0; i < plotDiv.data.length; i++) {
		const trace = plotDiv.data[i];
		const len = (trace.x && trace.x.length) || (trace.y ? trace.y.length : 0);
		reset["marker.color"][i] = new Array(len).fill(defaultBarColor);
	}
	Plotly.restyle(plotDiv, reset);

	// вернуть подписи
	Plotly.relayout(plotDiv, { "xaxis.tickfont.color": normalLabelColor });
}
