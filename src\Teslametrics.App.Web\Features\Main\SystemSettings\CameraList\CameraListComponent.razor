@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
@inherits InteractiveBaseComponent
<MudStack Spacing="3">
    <!-- Секция добавления новой камеры -->
    <MudPaper Class="pa-4"
              Outlined="true">
        <MudStack Row="true"
                  AlignItems="AlignItems.End"
                  Spacing="3">
            <MudAutocomplete T="CamModel"
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             AdornmentColor="Color.Primary"
                             Adornment="Adornment.Start"
                             SearchFunc="@SearchAsync"
                             Label="Выберите камеру для добавления"
                             ToStringFunc="@(e => e == null ? null : e.Name)"
                             @bind-Value="_selectedCamera"
                             Clearable="true"
                             ResetValueOnEmptyText="true"
                             Class="flex-grow-1" />
            <MudButton StartIcon="@Icons.Material.Outlined.Add"
                       OnClick="AddCamera"
                       Color="Color.Primary"
                       Variant="Variant.Filled"
                       Disabled="@(_selectedCamera == null)">
                Добавить камеру
            </MudButton>
        </MudStack>
    </MudPaper>

    <!-- Список камер -->
    <MudPaper Outlined="true">
        <MudList T="CamModel">
            <MudListSubheader>
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Videocam"
                             Class="mr-3" />
                    <MudText Typo="Typo.subtitle1">Камеры в комнате (@Cameras.Count)</MudText>
                </div>
            </MudListSubheader>

            @if (Cameras.Count == 0)
            {
                <MudListItem>
                    <div class="pa-4 text-center">
                        <MudIcon Icon="@Icons.Material.Outlined.VideocamOff"
                                 Size="Size.Large"
                                 Color="Color.Surface"
                                 Class="mb-2" />
                        <MudText Typo="Typo.body2"
                                 Color="Color.Surface">
                            В комнате нет камер
                        </MudText>
                        <MudText Typo="Typo.caption"
                                 Color="Color.Surface">
                            Используйте поле выше для добавления камер
                        </MudText>
                    </div>
                </MudListItem>
            }
            else
            {
                @foreach (var camera in Cameras)
                {
                    <MudListItem @key="@camera.Id">
                        <div class="d-flex align-center w-100">
                            <MudIcon Icon="@Icons.Material.Filled.Videocam"
                                     Color="Color.Primary"
                                     Class="mr-3" />
                            <div class="flex-grow-1">
                                <MudText Typo="Typo.body1">@camera.Name</MudText>
                                <MudText Typo="Typo.caption"
                                         Color="Color.Surface">
                                    ID: @camera.Id.ToString()[..8]...
                                </MudText>
                            </div>
                            <MudTooltip Text="Удалить камеру из комнаты">
                                <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="() => RemoveCamera(camera)" />
                            </MudTooltip>
                        </div>
                    </MudListItem>

                    @if (camera != Cameras.Last())
                    {
                        <MudDivider />
                    }
                }
            }
        </MudList>
    </MudPaper>
</MudStack>
