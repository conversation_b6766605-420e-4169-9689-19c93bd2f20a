::deep .metric_tile {
    background: var(--color-bg-2);
    cursor: pointer;
    transition: 0.3s;
}
::deep .metric_tile:hover {
    background: var(--color-primary-25);
}

::deep .info_container::before {
    content: " ";
    width: 4px;
    border-radius: 2px;
}

::deep .metric_tile.error .info_container::before {
    background: var(--mud-palette-error);
}

::deep .metric_tile.success .info_container::before {
    background: var(--mud-palette-success);
}

::deep .title {
    color: var(--color-text-input-tile);
}

::deep .subtitle {
    color: var(--color-text-placeholder);
}

::deep .icon_container {
    width: 24px;
    height: 24px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

::deep .metric_tile.error .icon_container {
    background: var(--mud-palette-error);
}

::deep .metric_tile.success .icon_container {
    background: var(--mud-palette-success);
}

::deep .icon {
    color: var(--color-neutral-100);
}