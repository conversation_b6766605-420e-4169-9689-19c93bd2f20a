using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

public interface IWirenboardSensorDataGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Настраивает список топиков для сенсоров (подписывается на новые и отписывается от ненужных)
    /// Если клиент не запущен, то запускает его
    /// </summary>
    /// <param name="request">Новый список топиков с указанием типа данных</param>
    Task ConfigureSensorTopicsAsync(ConfigureSensorTopicsRequest request);

    Task SubscribeAsync(SubscribeRequest request);

    Task UnsubscribeAsync(UnsubscribeRequest request);

    [GenerateSerializer]
    public record ConfigureSensorTopicsRequest(IEnumerable<SensorTopicInfo> TopicInfos);

    [GenerateSerializer]
    public record SubscribeRequest(string Topic, ISensorObserver Observer);

    [GenerateSerializer]
    public record UnsubscribeRequest(ISensorObserver Observer);
}

[GenerateSerializer]
public record SensorTopicInfo
{
    [Id(0)]
    public string Topic { get; init; }

    [Id(1)]
    public SensorValueType ValueType { get; init; }

    [Id(2)]
    public ISensorModel SensorModel { get; init; }

    // Местоположение и идентификаторы
    [Id(3)]
    public Guid CityId { get; init; }

    [Id(4)]
    public string City { get; init; }

    [Id(5)]
    public Guid BuildingId { get; init; }

    [Id(6)]
    public string Building { get; init; }

    [Id(7)]
    public Guid FloorId { get; init; }

    [Id(8)]
    public int Floor { get; init; }

    [Id(9)]
    public Guid RoomId { get; init; }

    [Id(10)]
    public string Room { get; init; }

    [Id(11)]
    public Guid? DeviceId { get; init; }

    [Id(12)]
    public string? Device { get; init; }

    [Id(13)]
    public Guid SensorId { get; init; }

    [Id(14)]
    public string? SensorName { get; init; }

    public SensorTopicInfo(string topic,
                    SensorValueType valueType,
                    ISensorModel sensorModel,
                    Guid cityId,
                    string city,
                    Guid buildingId,
                    string building,
                    Guid floorId,
                    int floor,
                    Guid roomId,
                    string room,
                    Guid? deviceId,
                    string? device,
                    Guid sensorId,
                    string? sensorName)
    {
        Topic = topic.Trim();
        ValueType = valueType;
        SensorModel = sensorModel;
        CityId = cityId;
        City = city;
        BuildingId = buildingId;
        Building = building;
        FloorId = floorId;
        Floor = floor;
        RoomId = roomId;
        Room = room;
        DeviceId = deviceId;
        Device = device;
        SensorId = sensorId;
        SensorName = sensorName;
    }
}