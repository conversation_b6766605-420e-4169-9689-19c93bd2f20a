// Класс для экспорта графиков дашборда в PDF
class ChartsPDFExporter {
	constructor() {
		this.html2pdfReady = null;
		this.html2canvasReady = null;
		// Селекторы контейнеров графиков (MudPaper) для захвата с заголовками и легендой
		this.chartSelectors = [
			{
				id: "incidents-count-chart-paper",
				title: "Происшествия",
				chartId: "incidents-chart",
			},
			{
				id: "incident-types-chart-paper",
				title: "Типы происшествий",
				chartId: "incident-types-chart",
			},
			{
				id: "equipment-incidents-top-chart-paper",
				title: "Проблемное оборудование",
				chartId: "equipment-incidents-top-chart",
			},
			{
				id: "temperature-chart-paper",
				title: "Дина<PERSON>и<PERSON>а температуры",
				chartId: "temperature-chart",
			},
			{
				id: "humidity-chart-paper",
				title: "Динамика влажности",
				chartId: "humidity-chart",
			},
			{
				id: "door-openings-chart-paper",
				title: "Открытия дверей",
				chartId: "door-openings-chart",
			},
		];
	}

	async ensureHtml2Pdf() {
		if (window.html2pdf) return;
		if (this.html2pdfReady) return this.html2pdfReady;

		this.html2pdfReady = new Promise((resolve, reject) => {
			const script = document.createElement("script");
			script.src =
				"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js";
			script.onload = () => resolve();
			script.onerror = () => reject(new Error("Failed to load html2pdf.js"));
			document.head.appendChild(script);
		});

		return this.html2pdfReady;
	}

	async ensureHtml2Canvas() {
		if (window.html2canvas) return;
		if (this.html2canvasReady) return this.html2canvasReady;

		this.html2canvasReady = new Promise((resolve, reject) => {
			const script = document.createElement("script");
			script.src =
				"https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js";
			script.onload = () => resolve();
			script.onerror = () => reject(new Error("Failed to load html2canvas.js"));
			document.head.appendChild(script);
		});

		return this.html2canvasReady;
	}

	async waitForPlotly() {
		return new Promise((resolve, reject) => {
			if (window.Plotly) {
				resolve();
				return;
			}

			let attempts = 0;
			const maxAttempts = 50;
			const checkInterval = setInterval(() => {
				attempts++;
				if (window.Plotly) {
					clearInterval(checkInterval);
					resolve();
				} else if (attempts >= maxAttempts) {
					clearInterval(checkInterval);
					reject(new Error("Plotly not loaded after maximum attempts"));
				}
			}, 100);
		});
	}

	async captureChart(chartConfig) {
		try {
			await this.ensureHtml2Canvas();

			// Ищем контейнер графика (MudPaper)
			let chartContainer = null;

			// Сначала пробуем найти по ID контейнера (если есть)
			if (chartConfig.id) {
				chartContainer = document.getElementById(chartConfig.id);
			}

			// Если не найден по ID контейнера, ищем по ID графика
			if (!chartContainer && chartConfig.chartId) {
				const chartElement = document.getElementById(chartConfig.chartId);
				if (chartElement) {
					// Ищем ближайший MudPaper контейнер
					chartContainer = chartElement.closest(".mud-paper");
				}
			}

			if (!chartContainer) {
				console.warn(
					`Chart container not found for: ${chartConfig.title} (${
						chartConfig.id || "no-id"
					} / ${chartConfig.chartId})`
				);
				return null;
			}

			// Проверяем, что в контейнере есть график
			const plotlyElement = chartContainer.querySelector(".js-plotly-plot");
			if (!plotlyElement) {
				console.warn(`No Plotly chart found in container: ${chartConfig.id}`);
				return null;
			}

			// Захватываем весь контейнер с заголовком и легендой
			const canvas = await window.html2canvas(chartContainer, {
				scale: 2, // Высокое качество для четких изображений
				useCORS: true,
				allowTaint: true,
				backgroundColor: "#ffffff",
				logging: false,
				width: chartContainer.offsetWidth,
				height: chartContainer.offsetHeight,
				// Настройки для сохранения стилей и цветов
				foreignObjectRendering: false, // Отключаем для лучшей совместимости
				imageTimeout: 15000, // Увеличиваем таймаут для загрузки изображений
				removeContainer: false, // Не удаляем контейнер преждевременно
				// Дополнительные настройки для корректного отображения
				onclone: (clonedDoc, element) => {
					// Применяем стили к клонированному документу для сохранения внешнего вида
					const clonedContainer = element;
					if (clonedContainer) {
						// Убеждаемся, что фон белый
						clonedContainer.style.backgroundColor = "#ffffff";
						clonedContainer.style.border = "1px solid #e0e0e0";
						clonedContainer.style.borderRadius = "8px";
						clonedContainer.style.padding = "16px";

						// Сохраняем стили легенды
						const legendElements = clonedContainer.querySelectorAll(".legend");
						legendElements.forEach((legend) => {
							legend.style.display = "flex";
							legend.style.alignItems = "center";
							legend.style.gap = "8px";
							legend.style.fontSize = "14px";
						});

						// Убеждаемся, что Plotly график отображается корректно
						const plotlyDiv = clonedContainer.querySelector(".js-plotly-plot");
						if (plotlyDiv) {
							plotlyDiv.style.backgroundColor = "transparent";
						}
					}
				},
			});

			// Конвертируем canvas в data URL
			const imageData = canvas.toDataURL("image/png", 1.0);

			return {
				title: chartConfig.title,
				imageData: imageData,
			};
		} catch (error) {
			console.error(`Error capturing chart ${chartConfig.id}:`, error);
			return null;
		}
	}

	async captureAllCharts() {
		await this.waitForPlotly();
		await this.ensureHtml2Canvas();

		const chartImages = [];

		for (const chartConfig of this.chartSelectors) {
			console.log(`Capturing chart: ${chartConfig.title} (${chartConfig.id})`);
			const imageInfo = await this.captureChart(chartConfig);
			if (imageInfo) {
				chartImages.push(imageInfo);
				console.log(`Successfully captured: ${chartConfig.title}`);
			} else {
				console.warn(`Failed to capture: ${chartConfig.title}`);
			}
		}

		console.log(`Total charts captured: ${chartImages.length}`);
		return chartImages;
	}

	async fetchCssText(url) {
		try {
			const resp = await fetch(url, { credentials: "include" });
			if (!resp.ok) throw new Error(`CSS load failed: ${url} (${resp.status})`);
			return await resp.text();
		} catch (error) {
			console.warn("CSS loading failed:", error.message);
			return "";
		}
	}

	async exportToPDF(html, options = {}) {
		try {
			await this.ensureHtml2Pdf();

			const {
				cssUrl = "/css/pdf-export.css",
				filenamePrefix = "dashboard_charts",
				orientation = "landscape",
			} = options;

			// Создаем контейнер для рендеринга
			const host = document.createElement("div");
			host.style.position = "fixed";
			host.style.left = "-10000px";
			host.style.top = "0";
			host.style.width = "297mm"; // A4 ширина в ландшафте
			host.style.zIndex = "-1";
			host.setAttribute("data-pdf-host", "true");

			// Загружаем CSS
			let inlinedCss = await this.fetchCssText(cssUrl);
			const styleTag = document.createElement("style");
			styleTag.setAttribute("data-inlined-pdf-css", "true");
			styleTag.textContent = `
                * { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                html, body { background: #fff !important; }
                ${inlinedCss}
            `;

			// Создаем контейнер с HTML
			const container = document.createElement("div");
			container.innerHTML = html;

			// Добавляем base для корректных путей
			const base = document.createElement("base");
			base.href = document.baseURI;

			// Собираем все в host
			host.appendChild(base);
			host.appendChild(styleTag);
			host.appendChild(container);
			document.body.appendChild(host);

			// Ждем загрузки изображений
			await this.waitForImages(container);

			// Настройки для html2pdf
			const pdfOptions = {
				margin: [10, 10, 10, 10],
				filename: `${filenamePrefix}_${
					new Date().toISOString().split("T")[0]
				}.pdf`,
				image: { type: "jpeg", quality: 0.98 },
				html2canvas: {
					scale: 2,
					useCORS: true,
					allowTaint: true,
					letterRendering: true,
					imageTimeout: 10000,
					backgroundColor: "rgba(255, 255, 255, 1)",
					logging: false,
					removeContainer: true,
				},
				jsPDF: {
					unit: "mm",
					format: "a4",
					orientation,
				},
				pagebreak: { mode: ["css", "legacy"] },
			};

			// Генерируем PDF
			await window.html2pdf().set(pdfOptions).from(container).save();

			// Убираем временный контейнер
			host.remove();

			return { success: true };
		} catch (error) {
			console.error("Error generating PDF:", error);
			return { success: false, error: error.message };
		}
	}

	async waitForImages(container) {
		const images = container.querySelectorAll("img");
		const promises = Array.from(images).map((img) => {
			return new Promise((resolve) => {
				if (img.complete) {
					resolve();
				} else {
					img.onload = resolve;
					img.onerror = resolve; // Продолжаем даже если изображение не загрузилось
					setTimeout(resolve, 5000); // Таймаут 5 секунд
				}
			});
		});

		await Promise.all(promises);
	}
}

// Экспорт для Blazor
const __chartsPDFExporter = new ChartsPDFExporter();

export async function captureChartsAndExportToPDF(html, options) {
	return await __chartsPDFExporter.exportToPDF(html, options);
}

export async function captureAllCharts() {
	return await __chartsPDFExporter.captureAllCharts();
}

// Функция для тестирования захвата графиков
export async function testChartCapture() {
	console.log("Testing chart capture...");
	const charts = await __chartsPDFExporter.captureAllCharts();
	console.log("Captured charts:", charts.length);
	charts.forEach((chart, index) => {
		console.log(
			`Chart ${index + 1}: ${chart.title}, Image size: ${
				chart.imageData.length
			} chars`
		);
	});
	return charts;
}
