﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Microsoft.Orleans.Reminders" Version="9.1.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.12.0-beta.1" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Sinks.Graylog" Version="3.1.1" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
    <PackageReference Include="MQTTnet" Version="5.0.1.1416" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FFMpegNET\FFMpegNET.csproj" />
    <ProjectReference Include="..\Orleans.LiveStreams\Orleans.LiveStreams.csproj" />
    <ProjectReference Include="..\Orleans.Streams.Kafka\Orleans.Streams.Kafka.csproj" />
    <ProjectReference Include="..\Teslametrics.Core\Teslametrics.Core.csproj" />
    <ProjectReference Include="..\Teslametrics.MediaServer.Contracts\Teslametrics.MediaServer.Contracts.csproj" />
    <ProjectReference Include="..\Teslametrics.Shared\Teslametrics.Shared.csproj" />
  </ItemGroup>
  
</Project>
