using Microsoft.Extensions.Options;
using Orleans.Utilities;
using System.Collections.Concurrent;
using System.Data;
using System.Reactive.Linq;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Domain.Incidents;
using Teslametrics.Core.Domain.Incidents.Events;
using Teslametrics.Core.Domain.Notifications;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.MediaServer.Notifications;
using Teslametrics.Core.Services.TransactionManager;
using static Teslametrics.MediaServer.Notifications.NotificationModule;
using Teslametrics.MediaServer.Mqtt;
using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

public class WirenboardSensorDataGrain : Grain, IWirenboardSensorDataGrain, IRemindable
{
    private readonly MqttClientService _mqttClientService;
    private readonly MediaServerModule.WirenboardSettings _settings;
    private readonly ILogger<WirenboardSensorDataGrain> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly ConcurrentDictionary<string, SensorTopicInfo> _activeTopics = new();
    private bool _isRunning = false;
    private bool _isReconnecting = false;
    private IDisposable? _messageSubscription;
    private readonly ObserverManager<ISensorObserver> _observerManager;
    private readonly ConcurrentDictionary<ISensorObserver, string> _subscribedTopics = new();
    private readonly ConcurrentDictionary<string, DoorState> _doorStates = new();
    private readonly ConcurrentDictionary<string, ISensorData> _lastTopicValues = new();
    private IGrainTimer? _doorCheckTimer;
    private const int DOOR_CHECK_INTERVAL_MS = 1000;
    private const string _keepAliveReminderName = "WirenboardSensorDataGrainKeepAliveReminder";
    private readonly TimeSpan _keepAliveReminderPeriod = TimeSpan.FromMinutes(10);
    private IGrainReminder? _keepAliveReminder;

    private class DoorState
    {
        public DateTimeOffset OpenTime { get; set; }
        public DoorModel Config { get; set; }
        public SensorTopicInfo TopicInfo { get; set; }

        public DoorState(DateTimeOffset openTime, DoorModel config, SensorTopicInfo topicInfo)
        {
            OpenTime = openTime;
            Config = config;
            TopicInfo = topicInfo;
        }
    }

    public WirenboardSensorDataGrain(ILogger<WirenboardSensorDataGrain> logger,
                                     IOptions<MediaServerModule.WirenboardSettings> options,
                                     MqttClientService mqttClientService,
                                     IServiceScopeFactory serviceScopeFactory,
                                     IHostApplicationLifetime applicationLifetime)
    {
        _logger = logger;
        _settings = options.Value;
        _mqttClientService = mqttClientService;
        _mqttClientService.OnConnected = OnMqttConnected;
        _mqttClientService.OnDisconnected = OnMqttDisconnected;
        _serviceScopeFactory = serviceScopeFactory;
        _applicationLifetime = applicationLifetime;

        _observerManager = new ObserverManager<ISensorObserver>(TimeSpan.FromMinutes(5), logger);

        SubscribeToMessages();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MqttGrain activated with ID: {GrainId}", this.GetPrimaryKeyString());

        // Настраиваем таймер для проверки дверей
        var options = new GrainTimerCreationOptions
        {
            Period = TimeSpan.FromMilliseconds(DOOR_CHECK_INTERVAL_MS),
            DueTime = TimeSpan.FromMilliseconds(DOOR_CHECK_INTERVAL_MS)
        };

        _doorCheckTimer = this.RegisterGrainTimer<object?>(
            CheckDoorsAsync,
            null,
            options);

        // Регистрируем напоминание для предотвращения деактивации зерна
        try
        {
            _keepAliveReminder = await this.RegisterOrUpdateReminder(
                _keepAliveReminderName,
                TimeSpan.FromMinutes(1), // Начальная задержка
                _keepAliveReminderPeriod // Период между вызовами
            );

            _logger.LogInformation("Registered keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _logger.LogInformation("MqttGrain deactivating with ID: {GrainId}, reason: {Reason}",
            this.GetPrimaryKeyString(), reason);

        // Удаляем таймер проверки дверей
        if (_doorCheckTimer != null)
        {
            _doorCheckTimer.Dispose();
            _doorCheckTimer = null;
        }

        // Удаляем напоминание
        if (_keepAliveReminder != null)
        {
            // Проверяем, не находится ли приложение в процессе завершения работы
            if (!_applicationLifetime.ApplicationStopping.IsCancellationRequested)
            {
                try
                {
                    await this.UnregisterReminder(_keepAliveReminder);
                    _logger.LogInformation("Unregistered keep-alive reminder for MqttGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("ReminderService has been stopped while unregistering reminder for WirenboardSensorDataGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to unregister keep-alive reminder for MqttGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
            }
            else
            {
                _logger.LogInformation("Skipping reminder unregistration for WirenboardSensorDataGrain with ID: {GrainId} as application is stopping",
                    this.GetPrimaryKeyString());
            }
        }

        _doorStates.Clear();

        if (_isRunning)
        {
            await ShutdownAsync();
        }

        _logger.LogInformation("MqttGrain resources cleaned up for ID: {GrainId}",
            this.GetPrimaryKeyString());

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    /// <summary>
    /// Метод, вызываемый системой Orleans при срабатывании напоминания
    /// </summary>
    /// <param name="reminderName">Имя напоминания</param>
    /// <param name="status">Статус напоминания</param>
    /// <returns>Task</returns>
    public Task ReceiveReminder(string reminderName, TickStatus status)
    {
        if (reminderName == _keepAliveReminderName)
        {
            _logger.LogDebug("Keep-alive reminder received for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        return Task.CompletedTask;
    }

    private async void OnMqttConnected()
    {
        try
        {
            _isReconnecting = false;

            await ResolveIncidentAsync(null, IncidentType.WirenboardDisconnected);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve wirenboard incident");
        }
    }

    private async void OnMqttDisconnected()
    {
        try
        {
            if (_isReconnecting)
            {
                return;
            }

            _isReconnecting = true;

            await CreateIncidentAsync(null, IncidentType.WirenboardDisconnected, DateTimeOffset.UtcNow);

            foreach (var topicInfo in _activeTopics.Values)
            {
                var data = new SensorNullData(topicInfo.Topic);
                await _observerManager.Notify(s => s.ReceiveData(data), s => _subscribedTopics.TryGetValue(s, out var topic) && topic == topicInfo.Topic);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create wirenboard incident");
        }
    }

    private async Task CheckDoorsAsync(object? _, CancellationToken cancellationToken)
    {
        var now = DateTimeOffset.UtcNow;

        foreach (var doorEntry in _doorStates)
        {
            var doorState = doorEntry.Value;
            var openDuration = now - doorState.OpenTime;

            if (openDuration.TotalSeconds > doorState.Config.AvailableOpeningTime)
            {
                try
                {
                    await CreateIncidentAsync(doorState.TopicInfo, IncidentType.Door, now);

                    _doorStates.TryRemove(doorEntry.Key, out var _);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "Failed to create door incident for topic {Topic}",
                        doorState.TopicInfo.Topic
                    );
                }
            }
        }
    }

    private async Task<bool> SubscribeTopicsAsync(IEnumerable<SensorTopicInfo> topicInfos)
    {
        try
        {
            // Отфильтровываем только новые топики, на которые еще не подписаны
            var newTopicInfos = topicInfos.Where(t => !_activeTopics.ContainsKey(t.Topic)).ToList();
            if (newTopicInfos.Count > 0)
            {
                var newTopicNames = newTopicInfos.Select(t => t.Topic).ToArray();

                await _mqttClientService.SubscribeAsync(newTopicNames);
                foreach (var topicInfo in newTopicInfos)
                {
                    _activeTopics[topicInfo.Topic] = topicInfo;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to topics: {Message}", ex.Message);
            return false;
        }
    }

    private async Task<bool> UnsubscribeTopicsAsync(IEnumerable<string> topics)
    {
        try
        {
            if (!_isRunning)
            {
                return false;
            }

            if (topics.Any())
            {
                await _mqttClientService.UnsubscribeAsync(topics.ToArray());
                foreach (var topic in topics)
                {
                    _activeTopics.TryRemove(topic, out _);

                    // Удаляем последнее значение для топика из кэша
                    if (_lastTopicValues.ContainsKey(topic))
                    {
                        _lastTopicValues.TryRemove(topic, out _);
                        _logger.LogDebug("Removed last known value for topic {Topic} from cache", topic);
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from topics: {Message}", ex.Message);
            return false;
        }
    }

    public async Task ConfigureSensorTopicsAsync(IWirenboardSensorDataGrain.ConfigureSensorTopicsRequest request)
    {
        if (_settings.BrokerAddress is null || _settings.Port is null)
        {
            _logger.LogError("Broker address or port is not configured");
            return;
        }

        try
        {
            var newTopics = request.TopicInfos.ToList();
            var newTopicNames = newTopics.Select(t => t.Topic).ToList();

            if (!_isRunning && newTopics.Count > 0)
            {
                await _mqttClientService.ConnectAsync(_settings.BrokerAddress!, _settings.Port!.Value, $"ClientId-{Guid.NewGuid()}");
                _isRunning = true;
            }

            var topicsToAdd = newTopics.Where(t => !_activeTopics.ContainsKey(t.Topic)).ToList();
            var topicsToRemove = _activeTopics.Keys.Where(t => !newTopicNames.Contains(t)).ToList();
            var existingTopics = _activeTopics.Keys.Where(newTopicNames.Contains).ToList();

            if (topicsToRemove.Count > 0)
            {
                await UnsubscribeTopicsAsync(topicsToRemove);
            }

            if (topicsToAdd.Count > 0)
            {
                await SubscribeTopicsAsync(topicsToAdd);
            }

            foreach (var topic in existingTopics)
            {
                _activeTopics[topic] = newTopics.First(t => t.Topic == topic);
            }

            // Если не осталось активных топиков, отключаемся
            if (_activeTopics.Count == 0)
            {
                await ShutdownAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating topics: {Message}", ex.Message);
        }
    }

    private async Task<bool> ShutdownAsync()
    {
        try
        {
            _logger.LogInformation("Shutting down MQTT client for grain ID: {GrainId}", this.GetPrimaryKeyString());

            // Отписываемся от сообщений
            _messageSubscription?.Dispose();
            _messageSubscription = null;

            if (_isRunning)
            {
                await _mqttClientService.DisconnectAsync();
                _isRunning = false;
                _activeTopics.Clear();
                _logger.LogInformation("MQTT client disconnected for grain ID: {GrainId}", this.GetPrimaryKeyString());
            }
            else
            {
                _logger.LogInformation("MQTT client was not connected for grain ID: {GrainId}", this.GetPrimaryKeyString());
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting MQTT client for grain ID: {GrainId}: {Message}",
                this.GetPrimaryKeyString(), ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Подписывается на сообщения MQTT и сохраняет их в TimescaleDB
    /// </summary>
    private void SubscribeToMessages()
    {
        _logger.LogInformation("Setting up MQTT message subscription for grain ID: {GrainId}", this.GetPrimaryKeyString());

        // Отписываемся от предыдущей подписки, если она существует
        if (_messageSubscription != null)
        {
            _logger.LogDebug("Disposing previous MQTT subscription for grain ID: {GrainId}", this.GetPrimaryKeyString());
            _messageSubscription.Dispose();
        }

        // Подписываемся на сообщения от MQTT клиента
        _messageSubscription = _mqttClientService.Messages
            .Subscribe(async message =>
            {
                try
                {
                    _logger.LogDebug("Received MQTT message for topic: {Topic}, grain ID: {GrainId}",
                        message.Topic, this.GetPrimaryKeyString());

                    var topicInfo = _activeTopics[message.Topic];

                    var data = topicInfo.ValueType switch
                    {
                        SensorValueType.String => (ISensorData)new SensorStringData(message.Topic, message.Value),
                        SensorValueType.Bool => new SensorBoolData(message.Topic, ParseBooleanValue(message.Value)),
                        SensorValueType.Double => new SensorDoubleData(message.Topic, double.Parse(message.Value, System.Globalization.CultureInfo.InvariantCulture)),
                        SensorValueType.Integer => new SensorIntData(message.Topic, int.Parse(message.Value, System.Globalization.CultureInfo.InvariantCulture)),
                        _ => throw new ArgumentOutOfRangeException(topicInfo.ValueType.ToString(), "Unknown sensor value type")
                    };

                    // Сохраняем последнее значение для топика
                    _lastTopicValues[message.Topic] = data;

                    await _observerManager.Notify(s => s.ReceiveData(data), s => _subscribedTopics.TryGetValue(s, out var topic) && topic == message.Topic);

                    await ProcessSensorData(data, topicInfo, message.Timestamp);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing MQTT message for topic: {Topic}, grain ID: {GrainId}: {Message}",
                        message.Topic, this.GetPrimaryKeyString(), ex.Message);
                }
            });

        _logger.LogInformation("MQTT message subscription set up for grain ID: {GrainId}", this.GetPrimaryKeyString());
    }

    /// <summary>
    /// Преобразует строковое значение в логическое с поддержкой различных форматов
    /// </summary>
    /// <param name="value">Строковое представление логического значения</param>
    /// <returns>Логическое значение</returns>
    private static bool ParseBooleanValue(string value)
    {
        // Приводим к нижнему регистру для сравнения без учета регистра
        value = value.Trim().ToLowerInvariant();

        // Проверяем различные форматы представления "истины"
        return value switch
        {
            "true" or "1" => true,
            "false" or "0" => false,
            _ => throw new FormatException($"String '{value}' was not recognized as a valid Boolean.")
        };
    }

    private async Task ProcessSensorData(ISensorData data, SensorTopicInfo topicInfo, DateTimeOffset timestamp)
    {
        bool isAlertCondition = false;

        switch (data)
        {
            case SensorDoubleData temperatureData when topicInfo.SensorModel is TemperatureModel tempConfig:
                isAlertCondition = temperatureData.Value > tempConfig.MaxTemp || temperatureData.Value < tempConfig.MinTemp;
                break;

            case SensorDoubleData humidityData when topicInfo.SensorModel is HumidityModel humidityConfig:
                isAlertCondition = humidityData.Value > humidityConfig.MaxHumidity || humidityData.Value < humidityConfig.MinHumidity;
                break;

            case SensorBoolData doorData when topicInfo.SensorModel is DoorModel doorConfig:
                await ProcessDoorStateAsync(doorData, doorConfig, topicInfo, timestamp);
                return;

            case SensorBoolData powerData when topicInfo.SensorModel is PowerModel:
                isAlertCondition = !powerData.Value;
                break;

            case SensorBoolData leakData when topicInfo.SensorModel is LeakModel:
                isAlertCondition = leakData.Value;
                break;
        }

        try
        {
            if (isAlertCondition)
            {
                await CreateIncidentAsync(topicInfo, GetIncidentTypeForData(topicInfo.SensorModel), timestamp);
            }
            else
            {
                await ResolveIncidentAsync(topicInfo, GetIncidentTypeForData(topicInfo.SensorModel));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process incident for topic {Topic}",
                topicInfo.Topic
            );
        }
    }

    private async Task ProcessDoorStateAsync(SensorBoolData doorData, DoorModel doorConfig, SensorTopicInfo topicInfo, DateTimeOffset timestamp)
    {
        var topic = doorData.Topic;
        var isOpen = doorData.Value;

        if (isOpen)
        {
            // Если это новое открытие двери
            if (!_doorStates.ContainsKey(topic))
            {
                _doorStates[topic] = new DoorState(timestamp, doorConfig, topicInfo);
            }
        }
        else
        {
            // Если дверь закрыта, удаляем её из отслеживания
            _doorStates.TryRemove(topic, out _);

            try
            {
                await ResolveIncidentAsync(topicInfo, IncidentType.Door);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Failed to resolve door incident for topic {Topic}",
                    topic
                );
            }
        }
    }

    public async Task CreateIncidentAsync(SensorTopicInfo? topicInfo, IncidentType incidentType, DateTimeOffset timestamp)
    {
        using var scope = _serviceScopeFactory.CreateScope();

        var incidentRepository = scope.ServiceProvider.GetRequiredService<IIncidentRepository>();
        var incidentNotificationRepository = scope.ServiceProvider.GetRequiredService<IIncidentNotificationRepository>();
        var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();
        var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();
        var transactionManager = scope.ServiceProvider.GetRequiredService<ITransactionManager>();

        using var transaction = await transactionManager.CreateTransactionAsync();

        var existingIncident = await incidentRepository.GetLastUnresolvedIncidentByTypeAsync(topicInfo?.Topic ?? string.Empty, incidentType);

        if (existingIncident != null)
        {
            return;
        }

        var incident = IncidentAggregate.Create(
            id: GuidGenerator.New(),
            incidentType: incidentType,
            cityId: topicInfo?.CityId ?? Guid.Empty,
            city: topicInfo?.City ?? string.Empty,
            buildingId: topicInfo?.BuildingId ?? Guid.Empty,
            building: topicInfo?.Building ?? string.Empty,
            floorId: topicInfo?.FloorId ?? Guid.Empty,
            floor: topicInfo?.Floor ?? 0,
            roomId: topicInfo?.RoomId ?? Guid.Empty,
            room: topicInfo?.Room ?? string.Empty,
            deviceId: topicInfo?.DeviceId ?? Guid.Empty,
            device: topicInfo?.Device ?? string.Empty,
            sensorId: topicInfo?.SensorId ?? Guid.Empty,
            topic: topicInfo?.Topic ?? string.Empty,
            createdAt: timestamp
        );

        await incidentRepository.AddAsync(incident);

        // Создаем уведомления для всех пользователей
        var notifications = await CreateIncidentNotification(incident, userRepository, isResolved: false);
        foreach (var notification in notifications)
        {
            await incidentNotificationRepository.AddAsync(notification);
        }

        // Ограничиваем количество уведомлений для каждого пользователя
        var userIds = notifications.Select(n => n.UserId).Distinct();
        foreach (var userId in userIds)
        {
            await incidentNotificationRepository.LimitNotificationsForUserAsync(userId);
        }

        // Отправляем событие в outbox
        var incidentCreatedEvent = new IncidentCreatedEvent(incident.Id,
                                                            incident.CityId,
                                                            incident.BuildingId,
                                                            incident.FloorId,
                                                            incident.RoomId,
                                                            incident.DeviceId,
                                                            incident.IncidentType,
                                                            incident.CreatedAt);
        await outbox.AddRangeAsync([incidentCreatedEvent]);

        await incidentRepository.SaveChangesAsync();
        await transaction.CommitAsync();

        // Отправляем email уведомления асинхронно после коммита транзакции
        _ = Task.Run(async () =>
        {
            try
            {
                // Проверяем конфигурацию email
                if (!IsEmailSenderConfigured)
                {
                    _logger.LogDebug("Email sender is not configured. Skipping email notifications for incident {IncidentId}", incident.Id);
                    return;
                }

                // Создаем новый scope для email уведомлений
                using var emailScope = _serviceScopeFactory.CreateScope();
                var emailNotificationService = emailScope.ServiceProvider.GetRequiredService<IIncidentEmailNotificationService>();
                await emailNotificationService.SendIncidentNotificationAsync(incident, topicInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email notifications for incident {IncidentId}", incident.Id);
            }
        });
    }

    public async Task ResolveIncidentAsync(SensorTopicInfo? topicInfo, IncidentType incidentType)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var transactionManager = scope.ServiceProvider.GetRequiredService<ITransactionManager>();
        var incidentRepository = scope.ServiceProvider.GetRequiredService<IIncidentRepository>();
        var incidentNotificationRepository = scope.ServiceProvider.GetRequiredService<IIncidentNotificationRepository>();
        var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();
        var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();

        using var transaction = await transactionManager.CreateTransactionAsync();

        // Получаем последний неразрешенный инцидент для данного топика с ожидаемым типом
        var existingIncident = await incidentRepository.GetLastUnresolvedIncidentByTypeAsync(topicInfo?.Topic ?? string.Empty, incidentType);

        if (existingIncident != null)
        {
            existingIncident.Resolve();

            // Создаем уведомления для всех пользователей
            var notifications = await CreateIncidentNotification(existingIncident, userRepository, isResolved: true);
            foreach (var notification in notifications)
            {
                await incidentNotificationRepository.AddAsync(notification);
            }

            // Ограничиваем количество уведомлений для каждого пользователя
            var userIds = notifications.Select(n => n.UserId).Distinct();
            foreach (var userId in userIds)
            {
                await incidentNotificationRepository.LimitNotificationsForUserAsync(userId);
            }

            await outbox.AddRangeAsync([new IncidentResolvedEvent(existingIncident.Id,
                                                                  existingIncident.CityId,
                                                                  existingIncident.BuildingId,
                                                                  existingIncident.FloorId,
                                                                  existingIncident.RoomId,
                                                                  existingIncident.DeviceId,
                                                                  existingIncident.IncidentType,
                                                                  existingIncident.CreatedAt)]);

            await incidentRepository.SaveChangesAsync();
            await transaction.CommitAsync();

            // Отправляем email уведомления о разрешении инцидента асинхронно после коммита транзакции
            _ = Task.Run(async () =>
            {
                try
                {
                    // Проверяем конфигурацию email
                    if (!IsEmailSenderConfigured)
                    {
                        _logger.LogDebug("Email sender is not configured. Skipping email notifications for resolved incident {IncidentId}", existingIncident.Id);
                        return;
                    }

                    // Создаем новый scope для email уведомлений
                    using var emailScope = _serviceScopeFactory.CreateScope();
                    var emailNotificationService = emailScope.ServiceProvider.GetRequiredService<IIncidentEmailNotificationService>();
                    await emailNotificationService.SendIncidentResolvedNotificationAsync(existingIncident, topicInfo);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send email notifications for resolved incident {IncidentId}", existingIncident.Id);
                }
            });
        }
    }

    private static IncidentType GetIncidentTypeForData(ISensorModel model)
    {
        return model switch
        {
            TemperatureModel => IncidentType.Temperature,
            HumidityModel => IncidentType.Humidity,
            DoorModel => IncidentType.Door,
            PowerModel => IncidentType.Power,
            LeakModel => IncidentType.Leak,
            _ => throw new ArgumentException($"Unknown sensor type: {model.GetType()}")
        };
    }

    /// <summary>
    /// Создает уведомления для всех пользователей на основе инцидента
    /// </summary>
    /// <param name="incident">Инцидент</param>
    /// <param name="userRepository">Репозиторий пользователей</param>
    /// <param name="isResolved">Указывает, создается ли уведомление для разрешенного инцидента</param>
    private static async Task<List<IncidentNotificationAggregate>> CreateIncidentNotification(IncidentAggregate incident, IUserRepository userRepository, bool isResolved)
    {
        // Получаем список всех пользователей из репозитория
        var users = await userRepository.GetAllUsersAsync();

        // Создаем уведомления для каждого пользователя
        var notifications = new List<IncidentNotificationAggregate>();

        var notificationType = GetNotificationTypeForIncident(incident.IncidentType, isResolved);

        foreach (var user in users)
        {
            var notification = IncidentNotificationAggregate.Create(
                id: GuidGenerator.New(),
                incidentId: incident.Id,
                userId: user.Id,
                notificationType: notificationType
            );

            notifications.Add(notification);
        }

        return notifications;
    }

    private static NotificationType GetNotificationTypeForIncident(IncidentType incidentType, bool isResolved)
    {
        return incidentType switch
        {
            IncidentType.Temperature => isResolved ? NotificationType.TemperatureIsNormal : NotificationType.TemperatureIsOutOfRange,
            IncidentType.Humidity => isResolved ? NotificationType.HumidityIsNormal : NotificationType.HumidityIsOutOfRange,
            IncidentType.Door => isResolved ? NotificationType.DoorClosed : NotificationType.DoorOpened,
            IncidentType.Leak => isResolved ? NotificationType.LeakFixed : NotificationType.LeakDetected,
            IncidentType.Power => isResolved ? NotificationType.PowerIsRestored : NotificationType.PowerIsLost,
            IncidentType.WirenboardDisconnected => isResolved ? NotificationType.WirenboardConnected : NotificationType.WirenboardDisconnected,
            _ => throw new ArgumentOutOfRangeException(incidentType.ToString(), "Unknown incident type")
        };
    }

    public async Task SubscribeAsync(IWirenboardSensorDataGrain.SubscribeRequest request)
    {
        _observerManager.Subscribe(request.Observer, request.Observer);
        _observerManager.ClearExpired();

        if (!_subscribedTopics.TryGetValue(request.Observer, out _))
        {
            // Отправляем последнее известное значение для топика, если оно есть
            if (_lastTopicValues.TryGetValue(request.Topic, out var lastValue))
            {
                try
                {
                    await request.Observer.ReceiveData(lastValue);
                    _logger.LogDebug("Sent last known value for topic {Topic} to new subscriber", request.Topic);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send last known value for topic {Topic} to new subscriber", request.Topic);
                }
            }
        }

        _subscribedTopics[request.Observer] = request.Topic;
    }

    public Task UnsubscribeAsync(IWirenboardSensorDataGrain.UnsubscribeRequest request)
    {
        _subscribedTopics.TryRemove(request.Observer, out _);
        _observerManager.Unsubscribe(request.Observer);
        _observerManager.ClearExpired();

        return Task.CompletedTask;
    }
}