import type { Emitter } from "mitt";

/**
 * Обрабатывает видеопоток, поступающий через MSE.
 * Управляет SourceBuffer, синхронизацией времени и событиями.
 *
 * Используется внутри PlayerCore.
 */
export type BufferRange = { start: number; end: number };

export type BufferSize = {
	size: number;
	threshold: number;
	maxSize: number;
	isCritical: boolean;
};

export interface IMediaPipeline {
	/** начало и конец буфера в секундах */
	bufferStart: number;
	bufferEnd: number;

	/** размер буфера в секундах */
	bufferSize: number;
	readonly ev: Emitter<Record<string, unknown>>;

	/**
	 * Добавляет сегмент потока в буфер.
	 * @param chunk Сырые данные сегмента (обычно .ts или .mp4)
	 * @param absTime Абсолютное (реальное) время начала сегмента
	 */
	push(chunk: Uint8Array, absTime?: Date): void;

	/**
	 * Выполняет seek к заданному абсолютному времени.
	 * Используется для режима воспроизведения `point` или `range`.
	 * @param time Абсолютное время
	 */
	seekAbs(time: Date): void;

	/**
	 * Перемещает воспроизведение на live-край (последние секунды буфера).
	 */
	seekLive(): void;

	/**
	 * Абсолютное (реальное) время текущей позиции воспроизведения.
	 * Может быть null, если синхронизация не была выполнена.
	 */
	readonly currentAbsTime: Date | null;

	/**
	 * Колбэк, вызываемый на каждое обновление времени (примерно раз в 250 мс, но зависит от FPS и <video>).
	 * Используется плагинами или UI для отслеживания текущего времени.
	 */
	onTime?: (t: Date) => void;

	/**
	 * Очищает буфер.
	 */
	clearBuffer(): void;

	/**
	 * Полностью освобождает ресурсы, таймеры и подписки.
	 */
	dispose(): void;
}
