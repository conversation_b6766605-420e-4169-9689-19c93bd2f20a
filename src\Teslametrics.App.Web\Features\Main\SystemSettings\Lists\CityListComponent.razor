@using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage
<MudList T="CityModel"
         SelectedValue="SelectedCity"
         SelectedValueChanged="SelectedValueChanged">
    <MudListSubheader Class="d-flex">
        Список городов
        <MudSpacer />
        <MudButton StartIcon="@Icons.Material.Outlined.Add"
                   OnClick="@(() => AddCity())"
                   Color="Color.Primary">
            Добавить город
        </MudButton>
    </MudListSubheader>

    @foreach (var item in Cities)
    {
        <MudListItem Value="@item"
                     Text="@(string.IsNullOrEmpty(item.Name) ? "Нет названия города" : item.Name)"
                     Icon="@Icons.Material.Filled.Room"
                     @key="@item">
            <ChildContent>
                <div class="d-flex flex-row align-center gap-3">
                    <div class="d-flex flex-column px-6 py-2">
                        <MudText Typo="Typo.body1">
                            Город: @item.Name
                        </MudText>
                    </div>
                    <MudSpacer />
                    <MudTooltip Text="Удалить город">
                        <MudIconButton Icon="@Icons.Material.Outlined.Delete"
                                       Color="Color.Warning"
                                       OnClick="@(() => RemoveCity(item))" />
                    </MudTooltip>
                </div>
            </ChildContent>
        </MudListItem>
    }

    @if (Cities.Count == 0)
    {
        <div class="pa-4">
            <NoItemsFoundComponent HasItems="false" />
        </div>
    }
</MudList>
