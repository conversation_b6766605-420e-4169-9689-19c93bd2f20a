using Teslametrics.Core.Abstractions;

namespace Teslametrics.Core.Domain.Notifications;

/// <summary>
/// Интерфейс репозитория для работы с уведомлениями об инцидентах
/// </summary>
public interface IIncidentNotificationRepository : IRepository<IncidentNotificationAggregate>
{
    /// <summary>
    /// Получает количество уведомлений для пользователя
    /// </summary>
    Task<int> GetNotificationCountForUserAsync(Guid userId,
                                               CancellationToken cancellationToken = default);

    /// <summary>
    /// Отмечает уведомление как прочитанное по идентификатору инцидента и идентификатору пользователя
    /// </summary>
    Task<bool> MarkAsReadByIncidentIdAndUserIdAsync(Guid incidentId,
                                                    Guid userId,
                                                    CancellationToken cancellationToken = default);

    /// <summary>
    /// Отмечает все уведомления пользователя как прочитанные
    /// </summary>
    Task<List<Guid>> MarkAllAsReadByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ограничивает количество уведомлений для пользователя до максимального значения,
    /// удаляя самые старые уведомления
    /// </summary>
    Task LimitNotificationsForUserAsync(Guid userId, int maxNotifications = 100, CancellationToken cancellationToken = default);
}
