using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using OtpNet;
using QRCoder;
using Teslametrics.Core.Domain.AccessControl.Users;

namespace Teslametrics.App.Web.Services.Authentication.TwoFactorAuth
{
    /// <summary>
    /// Сервис для работы с двухфакторной аутентификацией
    /// </summary>
    public class TwoFactorAuthService
    {
        private readonly ILogger<TwoFactorAuthService> _logger;

        // Количество цифр в TOTP коде
        private const int TotpDigits = 6;

        // Размер секретного ключа в байтах
        private const int SecretKeySize = 20;

        // Количество резервных кодов восстановления
        private const int RecoveryCodesCount = 10;

        // Длина резервного кода восстановления
        private const int RecoveryCodeLength = 8;

        public TwoFactorAuthService(ILogger<TwoFactorAuthService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Генерирует новый секретный ключ для TOTP
        /// </summary>
        public string GenerateSecretKey()
        {
            var secretKey = KeyGeneration.GenerateRandomKey(SecretKeySize);
            var base32Secret = Base32Encoding.ToString(secretKey);
            return base32Secret;
        }

        /// <summary>
        /// Создает URL для QR-кода, который можно отсканировать в приложении аутентификатора
        /// </summary>
        public string GenerateQrCodeUrl(string secretKey, string userName, string issuer)
        {
            // Создаем URL в формате otpauth://totp/{issuer}:{email}?secret={secretKey}&issuer={issuer}
            var encodedIssuer = Uri.EscapeDataString(issuer);
            var encodedName = Uri.EscapeDataString(userName);

            return $"otpauth://totp/{encodedIssuer}:{encodedName}?secret={secretKey}&issuer={encodedIssuer}&algorithm=SHA1&digits={TotpDigits}&period=30";
        }

        /// <summary>
        /// Генерирует QR-код на основе URL и возвращает его в формате base64
        /// </summary>
        /// <param name="secretKey">Секретный ключ</param>
        /// <param name="userName">Имя пользователя</param>
        /// <param name="issuer">Издатель (по умолчанию "Multimonitor")</param>
        /// <returns>QR-код в формате base64</returns>
        public string GenerateQrCodeBase64(string secretKey, string userName, string issuer = "Multimonitor")
        {
            // Получаем URL для QR-кода
            string qrCodeUrl = GenerateQrCodeUrl(secretKey, userName, issuer);
            
            // Создаем генератор QR-кода
            using var qrGenerator = new QRCodeGenerator();
            
            // Генерируем QR-код
            var qrCodeData = qrGenerator.CreateQrCode(qrCodeUrl, QRCodeGenerator.ECCLevel.Q);
            
            // Создаем QR-код в виде массива байтов PNG
            using var pngQrCode = new PngByteQRCode(qrCodeData);
            
            // Получаем массив байтов изображения QR-кода
            byte[] imageBytes = pngQrCode.GetGraphic(20); // 20 - размер пикселя
            
            // Возвращаем строку в формате base64 с префиксом для использования в HTML
            return $"data:image/png;base64,{Convert.ToBase64String(imageBytes)}";
        }

        /// <summary>
        /// Проверяет TOTP код, введенный пользователем
        /// </summary>
        public bool VerifyTotpCode(string secretKey, string totpCode)
        {
            try
            {
                var secretKeyBytes = Base32Encoding.ToBytes(secretKey);
                var totp = new Totp(secretKeyBytes, step: 30, mode: OtpHashMode.Sha1, totpSize: TotpDigits);

                // Проверяем код с учетом возможной задержки сети (1 шаг назад и 1 шаг вперед)
                return totp.VerifyTotp(totpCode, out _, VerificationWindow.RfcSpecifiedNetworkDelay);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Ошибка при проверке TOTP кода");
                return false;
            }
        }

        /// <summary>
        /// Генерирует набор резервных кодов восстановления
        /// </summary>
        public List<string> GenerateRecoveryCodes()
        {
            var recoveryCodes = new List<string>();

            for (int i = 0; i < RecoveryCodesCount; i++)
            {
                var code = GenerateRandomCode(RecoveryCodeLength);
                recoveryCodes.Add(code);
            }

            return recoveryCodes;
        }

        /// <summary>
        /// Хеширует резервные коды для безопасного хранения
        /// </summary>
        public List<string> HashRecoveryCodes(List<string> recoveryCodes)
        {
            var hashedCodes = new List<string>();

            foreach (var code in recoveryCodes)
            {
                var hashedCode = HashCode(code);
                hashedCodes.Add(hashedCode);
            }

            return hashedCodes;
        }

        /// <summary>
        /// Проверяет резервный код восстановления
        /// </summary>
        public bool VerifyRecoveryCode(string inputCode, List<string> hashedRecoveryCodes)
        {
            var hashedInputCode = HashCode(inputCode);
            return hashedRecoveryCodes.Contains(hashedInputCode);
        }

        /// <summary>
        /// Генерирует случайный код указанной длины
        /// </summary>
        private string GenerateRandomCode(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var code = new StringBuilder(length);

            for (int i = 0; i < length; i++)
            {
                code.Append(chars[random.Next(chars.Length)]);

                // Добавляем дефис после каждых 4 символов (кроме последней группы)
                if ((i + 1) % 4 == 0 && i < length - 1)
                {
                    code.Append('-');
                }
            }

            return code.ToString();
        }

        /// <summary>
        /// Хеширует код с использованием SHA256
        /// </summary>
        private string HashCode(string code)
        {
            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(code);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }
    }
}
