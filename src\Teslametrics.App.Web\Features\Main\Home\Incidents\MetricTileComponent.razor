﻿<div class="d_contents"
     @onclick="@OnClick"
     @onclick:stopPropagation="true">
    <MudPaper Elevation="0"
              Outlined="false"
              Class="@($"mud-width-full pa-4 br_16 metric_tile d-flex flex-row justify-space-between align-center gap-3 {(Error ? "error" : "success")}")">
        <div class="info_container d-flex flex-row gap-4">
            <MudStack Spacing="4">
                <MudText Typo="Typo.h1">@Value</MudText>
                <MudStack Spacing="3">
                    <MudText Typo="Typo.subtitle2"
                             Class="title">@Title</MudText>
                    <MudText Typo="Typo.body1"
                             Class="subtitle">@Subtitle</MudText>
                </MudStack>
            </MudStack>
        </div>
        <MudStack Class="pt-4"
                  AlignItems="AlignItems.End"
                  Spacing="6">
            <div class="icon_container">
                <MudIcon Icon="@TeslaIcons.Outlined.Fire"
                         Style="font-size: 1rem;"
                         Class="icon" />
            </div>
            <MudIconButton OnClick="@OnClick"
                           Icon="@Icons.Material.Filled.KeyboardArrowRight"
                           Size="Size.Medium"
                           Class="mr-n3 mt-n3" />
        </MudStack>
    </MudPaper>
</div>