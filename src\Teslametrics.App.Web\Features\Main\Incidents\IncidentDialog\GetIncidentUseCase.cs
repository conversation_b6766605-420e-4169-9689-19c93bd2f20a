using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog;

public static class GetIncidentUseCase
{
    public record Query(Guid IncidentId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public record IncidentModel(Guid SensorId, IncidentType? IncidentType, DateTimeOffset FiredAt, DateTimeOffset? ResolvedAt)
        {
            public bool IsResolved => ResolvedAt.HasValue;
        }

        public FridgeModel? Fridge { get; init; }

        public IncidentModel? Incident { get; init; }

        public List<CameraModel> Cameras { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IncidentModel incident, FridgeModel? fridge, List<CameraModel> cameras)
        {
            Incident = incident;
            Fridge = fridge;
            Cameras = cameras;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }
            Incident = null;
            Fridge = null;
            Cameras = [];
            Result = result;
        }

        public record FridgeModel(Guid Id, string Name, List<ISensorModel> Sensors);

        public record CameraModel(Guid Id, Guid? CameraStreamId, Guid OrganizationId);

        public interface ISensorModel
        {
            public Guid Id { get; }
            public string Name { get; }
            public string? DisplayName { get; }
        }

        public record TemperatureModel(Guid Id, string Name, string? DisplayName, float MinTemp, float MaxTemp) : ISensorModel;

        public record DoorModel(Guid Id, string Name, string? DisplayName, int AvailableOpeningTime) : ISensorModel;

        public record HumidityModel(Guid Id, string Name, string? DisplayName, float MinHumidity, float MaxHumidity) : ISensorModel;

        public record LeakModel(Guid Id, string Name, string? DisplayName) : ISensorModel;

        public record PowerModel(Guid Id, string Name, string? DisplayName) : ISensorModel;
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        IncidentNotFound,
        PlanNotFound,
        FridgeNotFound,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.IncidentId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.DeviceId)
                .Select(Db.Incidents.Props.SensorId)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where(Db.Incidents.Props.Id, ":IncidentId", SqlOperator.Equals, new { request.IncidentId })
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModel = await _dbConnection.QueryFirstOrDefaultAsync<IncidentModel>(template.RawSql, template.Parameters);
            if (incidentModel is null)
            {
                return new Response(Result.IncidentNotFound);
            }

            var incident = new Response.IncidentModel(incidentModel.SensorId,
                                                      incidentModel.IncidentType,
                                                      incidentModel.CreatedAt,
                                                      incidentModel.ResolvedAt);

            Response.FridgeModel? fridgeModel = null;
            List<Response.CameraModel> cameras = [];

            if (await CheckTableExistsAsync())
            {
                template = SqlQueryBuilder.Create()
                    .Select(Db.Plans.Props.Page)
                    .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

                var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

                if (string.IsNullOrEmpty(pageJson))
                {
                    return new Response(Result.PlanNotFound);
                }

                var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

                var fridge = page.GetFridge(incidentModel.DeviceId);

                if (fridge is not null)
                {
                    fridgeModel = new Response.FridgeModel(fridge.Id,
                                                           fridge.Name,
                                                           fridge.Sensors.Select(GetSensorModel).ToList());

                    var room = page.GetRoomByFridgeId(fridge.Id);

                    if (room is not null)
                    {
                        if (room.Cameras.Count > 0)
                        {
                            template = SqlQueryBuilder.Create()
                                .Select(Db.Cameras.Props.Id)
                                .Select(Db.Cameras.Props.OrganizationId)
                                .Where(Db.Cameras.Props.Id, ":CameraIds", SqlOperator.Any, new { cameraIds = room.Cameras.Select(c => c.Id).ToList() })
                                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

                            var cameraModels = await _dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

                            foreach (var camera in cameraModels)
                            {
                                var tableName = $"{Db.StreamSegments.Table}_{camera.Id.ToString("N")}";
                                template = SqlQueryBuilder.Create()
                                    .Select(Db.StreamSegments.Columns.SegmentIndex)
                                    .Where($"{Db.StreamSegments.Columns.StartTime} <= :IncidentTime AND :IncidentTime <= {Db.StreamSegments.Columns.EndTime}", new { IncidentTime = incident.FiredAt })
                                    .Build(QueryType.Standard, tableName, RowSelection.AllRows);

                                var segmentIndex = await _dbConnection.QueryFirstOrDefaultAsync<int?>(template.RawSql, template.Parameters);

                                if (segmentIndex is not null)
                                {
                                    var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
                                    var statusResponse = await mediaServerGrain.GetStatusesAsync(new IMediaServerGrain.GetCameraStatusesRequest([camera.Id]));
                                    var status = statusResponse.Statuses[camera.Id];

                                    Guid? cameraStreamId = null;

                                    if (status != CameraStatus.Stopped)
                                    {
                                        var cameraGrain = _clusterClient.GetGrain<ICameraGrain>(camera.Id);
                                        var response = await cameraGrain.GetCameraStreamIdAsync(new ICameraGrain.GetCameraStreamIdRequest(StreamType.Archive));
                                        cameraStreamId = response.CameraStreamId;
                                    }

                                    cameras.Add(new Response.CameraModel(camera.Id, cameraStreamId, camera.OrganizationId));
                                }
                            }
                        }
                    }
                }
            }

            return new Response(incident,
                                fridgeModel,
                                cameras);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }

        private static Response.ISensorModel GetSensorModel(ISensorModel sensorModel)
        {
            var model = sensorModel switch
            {
                TemperatureModel sensor => (Response.ISensorModel)new Response.TemperatureModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.MinTemp, sensor.MaxTemp),
                DoorModel sensor => new Response.DoorModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.AvailableOpeningTime),
                HumidityModel sensor => new Response.HumidityModel(sensor.Id, sensor.Name, sensor.DisplayName, sensor.MinHumidity, sensor.MaxHumidity),
                LeakModel sensor => new Response.LeakModel(sensor.Id, sensor.Name, sensor.DisplayName),
                PowerModel sensor => new Response.PowerModel(sensor.Id, sensor.Name, sensor.DisplayName),
                _ => throw new ArgumentException($"Unknown sensor type: {sensorModel.GetType()}")
            };

            return model;
        }
    }

    public record IncidentModel(Guid Id, Guid DeviceId, Guid SensorId, IncidentType IncidentType, DateTimeOffset CreatedAt, DateTimeOffset? ResolvedAt);
    public record CameraModel(Guid Id, Guid OrganizationId);
}