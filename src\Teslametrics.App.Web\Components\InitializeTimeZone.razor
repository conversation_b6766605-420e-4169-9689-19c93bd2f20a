﻿@using Teslametrics.App.Web.Services.BrowserTime
@inject TimeProvider Time
@inject IJSRuntime JS
@code {
    protected override async Task OnAfterRenderAsync(bool first)
    {
        if (first && Time is BrowserTimeProvider btp && btp.LocalTimeZone == TimeZoneInfo.Utc)
        {
            var iana = await JS.InvokeAsync<string>("Intl.DateTimeFormat().resolvedOptions().timeZone");
            btp.Set(iana); // теперь Time.LocalTimeZone «знает» браузер

            //var offset = _time.LocalTimeZone.GetUtcOffset(DateTimeOffset.UtcNow); - Получить смещение по UTC. Это даст TimeSpan, например +03:00 для Москвы.
        }
    }
}