using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Home.EquipmentIncidentsTop;

public partial class EquipmentIncidentsTopComponent
{
    // --- Render gate state ---
    private bool _allowRender = true;           // дать первый рендер
    private bool _prevHasData;
    private string _prevLegendKey = string.Empty;
    private bool _pendingBuildAfterRender;      // строить график после рендера
    private IEnumerable<IncidentType> _incidentTypes => _response?.Incidents.SelectMany(i => i.Counts.Keys).Distinct() ?? [];

    #region [Fields]
    private GetEquipmentIncidentsTopUseCase.Response? _response;
    private IJSObjectReference? _jsModule;
    private DotNetObjectReference<EquipmentIncidentsTopComponent>? _objRef;
    #endregion

    #region [Injectables]
    [Inject]
    private TimeProvider _time { get; set; } = null!;

    [Inject]
    private IJSRuntime _jSRuntime { get; set; } = null!;

    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter]
    public string Class { get; set; } = string.Empty;

    [Parameter]
    public int Limit { get; set; } = 5;
    #endregion

    #region [Methods]
    [JSInvokable]
    public async Task NavigateToIncident(Guid equipmentId)
    {
        var hasAccess = await CheckIfPermissionGranted(AppPermissions.Main.Incidents.Read.GetEnumPermissionString());
        if (!hasAccess) return;

        var query = new Dictionary<string, object?>
        {
            ["FridgeId"] = equipmentId,
        };

        var uri = _navigationManager.GetUriWithQueryParameters("/incidents", query);
        await _jSRuntime.InvokeVoidAsync("open", uri, "_blank");
    }

    #region [LifeCycle]
    protected override void OnInitialized()
    {
        _objRef = DotNetObjectReference.Create(this);
        base.OnInitialized();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();

        var hasData = HasData(_response);
        var legendKey = LegendKey(_incidentTypes);   // подпишите под ваши поля

        // Разрешаем рендер только когда это действительно нужно:
        // 1) меняется пустота данных (переход график <-> заглушка)
        // 2) меняется состав легенды (если легенда у вас в Razor)
        _allowRender = _allowRender              // первый рендер
                       || hasData != _prevHasData
                       || legendKey != _prevLegendKey;

        _prevHasData = hasData;
        _prevLegendKey = legendKey;

        // Сам график обновляем сразу, без рендера Razor
        // (DOM не меняется), а если надо показать заглушку —
        // строить график не нужно.
        if (_jsModule != null && hasData)
        {
            _pendingBuildAfterRender = true;
        }
        else
        {
            // если сейчас пусто, но контейнер мог быть отрисован раньше —
            // дадим Razor перерисоваться и убрать его
            _pendingBuildAfterRender = false;
        }

        await base.OnParametersSetAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _jsModule = await _jSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Home/EquipmentIncidentsTop/EquipmentIncidentsTopComponent.razor.js");

                // если после первого рендера у нас есть данные — построим график
                if (_prevHasData) await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                Snackbar.Add("Не удалось загрузить скрипт для построения графика",
                    MudBlazor.Severity.Error);
            }
        }

        // на случай, если вы решите строить график строго после рендера дом-узла:
        if (_pendingBuildAfterRender && _prevHasData && _jsModule != null)
        {
            _pendingBuildAfterRender = false;
            await BuildChartsAsync();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override bool ShouldRender()
    {
        // одноразово разрешаем текущий рендер и сбрасываем флаг
        var r = _allowRender;
        _allowRender = false;
        return r;
    }
    #endregion

    #region [Data]
    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            var offset = _time.LocalTimeZone.GetUtcOffset(DateTimeOffset.UtcNow);
            _response = await ScopeFactory.MediatorSend(new GetEquipmentIncidentsTopUseCase.Query(Limit, offset, null, null, null, null));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении данных о наиболее проблемном оборудовании. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
    }
    #endregion

    #region [Chart]
    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;
        try
        {
            await _jsModule.InvokeVoidAsync("initChart", _response?.Incidents, _objRef);
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось построить график проблемного оборудования на полученных данных", MudBlazor.Severity.Error);
        }
    }
    #endregion

    private async Task<bool> CheckIfPermissionGranted(string appPermissionString)
    {
        var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (userAuthState is null) return false;

        var policyRequirementResource = new PolicyRequirementResource(null, null);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            appPermissionString // The policy name
        );

        return authorizationResult.Succeeded;
    }
    #endregion

    private static bool HasData(GetEquipmentIncidentsTopUseCase.Response? resp)
        => resp?.Incidents is { Count: > 0 };

    // Сигнатура состава легенды.
    private static string LegendKey(IEnumerable<IncidentType> incidentTypes)
    {
        if (incidentTypes is null || !incidentTypes.Any()) return string.Empty;

        var keys = incidentTypes.OrderBy(s => s);
        return string.Join('|', keys);
    }
}
