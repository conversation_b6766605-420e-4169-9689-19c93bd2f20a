using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Lists;

public partial class FreezerListComponent
{
    [Parameter] public List<FreezerModel> Freezers { get; set; } = [];
    [Parameter] public FreezerModel? SelectedFreezer { get; set; }
    [Parameter] public EventCallback<FreezerModel?> OnFreezerSelected { get; set; }
    [Parameter] public Func<Task>? OnFreezerAdded { get; set; }
    [Parameter] public Func<FreezerModel, Task>? OnFreezerRemoved { get; set; }

    private async Task AddFreezer()
    {
        if (OnFreezerAdded != null)
            await OnFreezerAdded();
    }

    private async Task RemoveFreezer(FreezerModel freezer)
    {
        if (OnFreezerRemoved != null)
            await OnFreezerRemoved(freezer);
    }

    private Task SelectedValueChanged(FreezerModel value)
    {
        if (OnFreezerSelected.HasDelegate)
            return OnFreezerSelected.InvokeAsync(value);
        return Task.CompletedTask;
    }
}
