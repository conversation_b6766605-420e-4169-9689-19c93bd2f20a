using Teslametrics.Core.Abstractions;
using Teslametrics.Shared;

namespace Teslametrics.Core.Domain.Notifications;

/// <summary>
/// Агрегат для хранения уведомлений пользователей о инцидентах
/// </summary>
public class IncidentNotificationAggregate : IEntity
{
    /// <summary>
    /// Идентификатор уведомления
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Идентификатор инцидента, к которому относится уведомление
    /// </summary>
    public Guid IncidentId { get; private set; }

    /// <summary>
    /// Идентификатор пользователя, которому адресовано уведомление
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// Тип уведомления
    /// </summary>
    public NotificationType NotificationType { get; private set; }

    /// <summary>
    /// Флаг, указывающий, прочитано ли уведомление
    /// </summary>
    public bool IsRead { get; private set; }

    private IncidentNotificationAggregate()
    {
    }

    /// <summary>
    /// Создает новое уведомление для пользователя
    /// </summary>
    public static IncidentNotificationAggregate Create(Guid id,
                                                       Guid incidentId,
                                                       Guid userId,
                                                       NotificationType notificationType) =>
        new IncidentNotificationAggregate
        {
            Id = id,
            IncidentId = incidentId,
            UserId = userId,
            NotificationType = notificationType,
            IsRead = false
        };

    /// <summary>
    /// Отмечает уведомление как прочитанное
    /// </summary>
    public void MarkAsRead()
    {
        IsRead = true;
    }
}