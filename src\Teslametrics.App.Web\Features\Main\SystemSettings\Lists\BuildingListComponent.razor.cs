using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Lists;

public partial class BuildingListComponent
{
    [Parameter] public CityModel? City { get; set; }
    [Parameter] public List<BuildingModel> Buildings { get; set; } = [];
    [Parameter] public BuildingModel? SelectedBuilding { get; set; }
    [Parameter] public EventCallback<BuildingModel?> OnBuildingSelected { get; set; }
    [Parameter] public Func<Task>? OnBuildingAdded { get; set; }
    [Parameter] public Func<BuildingModel, Task>? OnBuildingRemoved { get; set; }

    private async Task AddBuilding()
    {
        if (OnBuildingAdded != null)
            await OnBuildingAdded();
    }

    private async Task RemoveBuilding(BuildingModel building)
    {
        if (OnBuildingRemoved != null)
            await OnBuildingRemoved(building);
    }

    private Task SelectedValueChanged(BuildingModel building)
    {
        if (OnBuildingSelected.HasDelegate)
            return OnBuildingSelected.InvokeAsync(building);
        return Task.CompletedTask;
    }
}
