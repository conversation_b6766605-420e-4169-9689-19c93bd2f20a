﻿@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.CSVExport
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.XLSXExport
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Export.PDFExport

<MudMenu Label="Скачать"
         Variant="Variant.Filled"
         StartIcon="@TeslaIcons.Actions.Import"
         EndIcon="@Icons.Material.Filled.KeyboardArrowDown">
    <CSVExportComponent IsResolved="IsResolved"
                        IncidentType="IncidentType"
                        DateFrom="DateFrom"
                        DateTo="DateTo"
                        CityId="@CityId"
                        BuildingId="@BuildingId"
                        FloorId="@FloorId"
                        RoomId="@RoomId"
                        FridgeId="@FridgeId" />
    <XLSXExportComponent IsResolved="IsResolved"
                         IncidentType="@IncidentType"
                         DateFrom="DateFrom"
                         DateTo="DateTo"
                         CityId="@CityId"
                         BuildingId="@BuildingId"
                         FloorId="@FloorId"
                         RoomId="@RoomId"
                         FridgeId="@FridgeId" />
    <PDFExportComponent IsResolved="IsResolved"
                        IncidentType="@IncidentType"
                        DateFrom="DateFrom"
                        DateTo="DateTo"
                        CityId="@CityId"
                        BuildingId="@BuildingId"
                        FloorId="@FloorId"
                        RoomId="@RoomId"
                        FridgeId="@FridgeId" />
</MudMenu>