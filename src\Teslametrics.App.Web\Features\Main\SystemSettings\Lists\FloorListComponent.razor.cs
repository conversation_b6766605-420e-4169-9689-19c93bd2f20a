using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;

namespace Teslametrics.App.Web.Features.Main.SystemSettings.Lists;

public partial class FloorListComponent
{
	[Parameter] public BuildingModel? Building { get; set; }
	[Parameter] public List<FloorModel> Floors { get; set; } = [];
	[Parameter] public FloorModel? SelectedFloor { get; set; }
	[Parameter] public EventCallback<FloorModel?> OnFloorSelected { get; set; }
	[Parameter] public Func<Task>? OnFloorAdded { get; set; }
	[Parameter] public Func<FloorModel, Task>? OnFloorRemoved { get; set; }

	private async Task AddFloor()
	{
		if (OnFloorAdded != null)
			await OnFloorAdded();
	}

	private async Task RemoveFloor(FloorModel floor)
	{
		if (OnFloorRemoved != null)
			await OnFloorRemoved(floor);
	}

	private Task SelectedValueChanged(FloorModel building)
	{
		if (OnFloorSelected.HasDelegate)
			return OnFloorSelected.InvokeAsync(building);
		return Task.CompletedTask;
	}
}
