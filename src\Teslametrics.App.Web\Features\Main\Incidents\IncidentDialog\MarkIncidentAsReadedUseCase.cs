using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Incidents.Events;
using Teslametrics.Core.Domain.Notifications;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog;

public static class MarkIncidentAsReadedUseCase
{
    public record Command(Guid IncidentId, Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response()
        {
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        IncidentNotFound,
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(q => q.IncidentId).NotEmpty();
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IIncidentNotificationRepository _incidentNotificationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IIncidentNotificationRepository incidentNotificationRepository,
                       ITransactionManager transactionManager,
                       IOutbox outbox)
        {
            _validator = validator;
            _incidentNotificationRepository = incidentNotificationRepository;
            _transactionManager = transactionManager;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();
            var isRead = await _incidentNotificationRepository.MarkAsReadByIncidentIdAndUserIdAsync(request.IncidentId, request.UserId, cancellationToken);

            if (isRead)
            {
                await _outbox.AddRangeAsync([new IncidentReadedEvent(request.IncidentId, request.UserId)]);
            }

            await transaction.CommitAsync();

            return new Response();
        }
    }
}
