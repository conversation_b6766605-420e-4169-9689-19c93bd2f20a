import {
	PlayerCoreEvent,
	type IPlayerCore,
	type IPluginBase,
	type Unsub,
} from "./Core";

class PlayPausePlugin implements IPluginBase {
	private _core: IPlayerCore | null = null;
	private _unsubPaused: Unsub | null = null;
	private _unsubPlay: Unsub | null = null;

	name: string;
	constructor() {
		this.name = "PlayPausePlugin";
	}

	play() {
		this._core?.resume();
	}

	pause() {
		this._core?.pause();
	}

	init(core: IPlayerCore): void | Promise<void> {
		this._core = core;

		this._unsubPaused = core.subscribe(PlayerCoreEvent.Paused, () => {
			console.log("Paused");
		});

		this._unsubPaused = core.subscribe(PlayerCoreEvent.SeekLive, () => {
			console.log("Paused");
		});
	}

	destroy(): void {
		this._unsubPaused?.();
		this._unsubPlay?.();
	}
}

// вернём сам конструктор (а не инстанс!)
export function getFactory() {
	return PlayPausePlugin;
}
