using Microsoft.AspNetCore.Components;
using static Teslametrics.App.Web.Features.Main.SystemSettings.SystemSettingsPage;
namespace Teslametrics.App.Web.Features.Main.SystemSettings.Lists;

public partial class RoomListComponent
{
    [Parameter] public FloorModel? Floor { get; set; }
    [Parameter] public BuildingModel? Building { get; set; }
    [Parameter] public List<RoomModel> Rooms { get; set; } = [];
    [Parameter] public RoomModel? SelectedRoom { get; set; }
    [Parameter] public EventCallback<RoomModel?> OnRoomSelected { get; set; }
    [Parameter] public Func<Task>? OnRoomAdded { get; set; }
    [Parameter] public Func<RoomModel, Task>? OnRoomRemoved { get; set; }

    private bool IsAddRoomDisabled => Building?.Floors.Count(x => x.Number == Floor?.Number) > 1;

    private async Task AddRoom()
    {
        if (OnRoomAdded != null)
            await OnRoomAdded();
    }

    private async Task RemoveRoom(RoomModel room)
    {
        if (OnRoomRemoved != null)
            await OnRoomRemoved(room);
    }

    private Task SelectedValueChanged(RoomModel value)
    {
        if (OnRoomSelected.HasDelegate)
            return OnRoomSelected.InvokeAsync(value);
        return Task.CompletedTask;
    }
}
