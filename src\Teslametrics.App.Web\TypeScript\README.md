# TypeScript Integration for Teslametrics Blazor Server

This document describes the TypeScript integration setup for the Teslametrics Blazor Server application.

## Overview

TypeScript support has been added to provide type-safe JavaScript development with modern ES2020 features. The setup allows you to write TypeScript files that are compiled to JavaScript and used in Blazor components through JavaScript interop.

## Project Structure

```
src/Teslametrics.App.Web/
├── TypeScript/                    # TypeScript source files
│   ├── demo.ts                   # Demo TypeScript module
│   └── README.md                 # This documentation
├── Components/                   # Component-specific TypeScript files
│   ├── ComponentHelper.ts        # Component utility functions
│   └── TypeScriptDemo.razor      # Demo Blazor component
├── Features/                     # Feature-specific TypeScript files
│   └── FeatureUtils.ts           # Feature utility functions
├── wwwroot/js/                   # Global JavaScript/TypeScript files
│   └── GlobalUtils.ts            # Global utility functions
├── wwwroot/js/compiled/          # Compiled JavaScript output
│   ├── TypeScript/               # Compiled from TypeScript/ directory
│   │   └── demo.js
│   ├── Components/               # Compiled from Components/ directory
│   │   └── ComponentHelper.js
│   ├── Features/                 # Compiled from Features/ directory
│   │   └── FeatureUtils.js
│   └── wwwroot/js/               # Compiled from wwwroot/js/ directory
│       └── GlobalUtils.js
└── tsconfig.json                 # TypeScript configuration
```

## Configuration Files

### tsconfig.json
The TypeScript configuration is set up with:
- **Target**: ES2020 for modern JavaScript features
- **Module**: ES2020 for native ES modules
- **Output Directory**: `./wwwroot/js/compiled`
- **Source Directories**: Multiple root directories using `rootDirs`
  - `./TypeScript` - Core TypeScript modules
  - `./Components` - Component-specific TypeScript files
  - `./Features` - Feature-specific TypeScript files
  - `./wwwroot/js` - Global utility TypeScript files
- **Path Mapping**: Configured for easy imports between directories
- **Strict Mode**: Enabled for maximum type safety
- **Source Maps**: Enabled for debugging

### Project File Changes
The `.csproj` file includes:
- **Microsoft.TypeScript.MSBuild** package for automatic compilation
- TypeScript files from multiple directories:
  - `TypeScript/**/*.ts`
  - `Components/**/*.ts`
  - `Features/**/*.ts`
  - `wwwroot/js/**/*.ts`
- Compiled JavaScript files are automatically included in the build output
- TypeScript compilation happens during the build process

## Multi-Directory Benefits

### Organized Code Structure
- **TypeScript/**: Core application TypeScript modules
- **Components/**: TypeScript files co-located with Blazor components
- **Features/**: Feature-specific TypeScript functionality
- **wwwroot/js/**: Global utilities and third-party integrations

### Advantages
1. **Co-location**: TypeScript files can be placed next to related Blazor components
2. **Feature Organization**: Group TypeScript code by application features
3. **Separation of Concerns**: Different types of functionality in appropriate directories
4. **Maintainability**: Easier to find and maintain related code
5. **Scalability**: Structure supports large applications with many features

### Import Paths
When importing modules in Blazor components, use the compiled paths:

```csharp
// From TypeScript/ directory
jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/compiled/TypeScript/demo.js");

// From Components/ directory
componentModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/compiled/Components/ComponentHelper.js");

// From Features/ directory
featureModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/compiled/Features/FeatureUtils.js");

// From wwwroot/js/ directory
globalModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/compiled/wwwroot/js/GlobalUtils.js");
```

## Development Workflow

### 1. Writing TypeScript Code

Create TypeScript files in the `TypeScript/` directory:

```typescript
// TypeScript/myModule.ts
export function processData(input: string): string {
    return `Processed: ${input}`;
}

export class DataProcessor {
    constructor(private prefix: string) {}
    
    process(data: string[]): string[] {
        return data.map(item => `${this.prefix}: ${item}`);
    }
}
```

### 2. Compilation

Since Node.js/npm is not available in this environment, TypeScript files need to be manually compiled to JavaScript. The compiled files should be placed in `wwwroot/js/compiled/`.

For future development with Node.js available:
```bash
# Install TypeScript globally
npm install -g typescript

# Compile TypeScript files
tsc

# Or watch for changes
tsc --watch
```

### 3. Using in Blazor Components

```csharp
@page "/my-page"
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

<button @onclick="CallTypeScriptFunction">Process Data</button>
<p>@result</p>

@code {
    private IJSObjectReference? jsModule;
    private string result = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>(
                "import", "./js/compiled/myModule.js");
        }
    }

    private async Task CallTypeScriptFunction()
    {
        if (jsModule != null)
        {
            result = await jsModule.InvokeAsync<string>("processData", "Hello World");
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (jsModule is not null)
        {
            await jsModule.DisposeAsync();
        }
    }
}
```

## Demo Component

The `TypeScriptDemo.razor` component demonstrates:

### Basic Features
- **Text Processing**: String manipulation with TypeScript functions
- **Time Formatting**: Russian locale date/time formatting
- **DOM Manipulation**: Dynamic element styling and creation
- **Mathematical Operations**: Type-safe number calculations
- **Array Processing**: Statistical operations on number arrays

### TypeScript Features Demonstrated
- **Classes and Interfaces**: Object-oriented programming
- **Type Safety**: Strict typing for function parameters and return values
- **Modern ES2020**: Arrow functions, template literals, destructuring
- **Module Exports**: ES module system for clean imports
- **Error Handling**: Type checking and validation

## Best Practices

### 1. Type Safety
```typescript
// Good: Explicit types
function calculateSum(a: number, b: number): number {
    return a + b;
}

// Better: Input validation
function calculateSum(a: number, b: number): number {
    if (isNaN(a) || isNaN(b)) {
        throw new Error('Invalid input: both parameters must be valid numbers');
    }
    return a + b;
}
```

### 2. Interface Definitions
```typescript
interface ProcessedResult {
    originalData: string[];
    processedData: string[];
    timestamp: Date;
}

export function processArray(data: string[]): ProcessedResult {
    return {
        originalData: data,
        processedData: data.map(item => item.toUpperCase()),
        timestamp: new Date()
    };
}
```

### 3. Error Handling
```typescript
export function safeOperation(input: unknown): string {
    try {
        if (typeof input !== 'string') {
            throw new Error('Input must be a string');
        }
        return input.toUpperCase();
    } catch (error) {
        console.error('Operation failed:', error);
        return 'Error occurred';
    }
}
```

### 4. Russian Localization
```typescript
export function formatDateTime(): string {
    const now = new Date();
    return now.toLocaleString('ru-RU', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
```

## Adding New TypeScript Files

### Choose the Appropriate Directory

1. **TypeScript/**: For core application modules and shared utilities
2. **Components/**: For TypeScript code specific to Blazor components
3. **Features/**: For feature-specific TypeScript functionality
4. **wwwroot/js/**: For global utilities and third-party integrations

### Steps to Add a New TypeScript File

1. **Create the TypeScript file** in the appropriate directory:
   ```typescript
   // Example: Components/MyComponent.ts
   export class MyComponentHelper {
       public static doSomething(elementId: string): void {
           // Implementation
       }
   }
   ```

2. **Build the project** - TypeScript compilation happens automatically:
   ```bash
   dotnet build
   ```

3. **Import in your Blazor component**:
   ```csharp
   private IJSObjectReference? myModule;

   protected override async Task OnAfterRenderAsync(bool firstRender)
   {
       if (firstRender)
       {
           myModule = await JSRuntime.InvokeAsync<IJSObjectReference>(
               "import", "./js/compiled/Components/MyComponent.js");
       }
   }
   ```

4. **Use JavaScript interop** to call the functions:
   ```csharp
   private async Task CallTypeScriptFunction()
   {
       if (myModule != null)
       {
           await myModule.InvokeVoidAsync("MyComponentHelper.doSomething", "elementId");
       }
   }
   ```

### Directory-Specific Guidelines

- **Components/**: Name files to match the related Blazor component
- **Features/**: Group related functionality in subdirectories if needed
- **TypeScript/**: Use descriptive names for shared modules
- **wwwroot/js/**: Follow existing naming conventions for global utilities

## Troubleshooting

### Common Issues

1. **Module not found**: Ensure the compiled JavaScript file exists in `wwwroot/js/compiled/`
2. **Function not exported**: Check that functions are properly exported with `export`
3. **Type errors**: Verify TypeScript compilation completed without errors
4. **Runtime errors**: Check browser console for JavaScript errors

### Debugging

- Use browser developer tools to inspect the compiled JavaScript
- Enable source maps in `tsconfig.json` for debugging TypeScript directly
- Add console.log statements for debugging function calls
- Use the browser's Network tab to verify module loading

## Future Enhancements

When Node.js becomes available:
1. Install TypeScript compiler via npm
2. Add build scripts to package.json
3. Set up automatic compilation on file changes
4. Add TypeScript linting with ESLint
5. Integrate with CI/CD pipeline for automatic compilation

## Navigation

The TypeScript demo is accessible via the main navigation menu under "TypeScript Demo" when the application is running.

## Implementation Summary

This multi-directory TypeScript integration provides:

✅ **Complete TypeScript Setup**: Configuration files and project structure
✅ **Multi-Directory Support**: TypeScript compilation from 4 different source directories
✅ **Type-Safe Development**: Strict TypeScript configuration with modern ES2020 features
✅ **Blazor Integration**: Seamless JavaScript interop with TypeScript-compiled modules
✅ **Organized Code Structure**: Co-location of TypeScript files with related components and features
✅ **Automatic Compilation**: MSBuild integration compiles all TypeScript files during build
✅ **Demo Implementation**: Comprehensive examples from all source directories
✅ **Documentation**: Complete setup and usage guidelines
✅ **Build Integration**: Project builds successfully with TypeScript files from multiple locations

### Supported Source Directories:
- **TypeScript/**: Core application modules
- **Components/**: Component-specific TypeScript files
- **Features/**: Feature-specific TypeScript functionality
- **wwwroot/js/**: Global utilities and third-party integrations

The implementation demonstrates modern TypeScript development practices with organized code structure while maintaining compatibility with the existing Blazor Server architecture.
