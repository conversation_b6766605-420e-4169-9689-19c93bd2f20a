﻿@using Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.SensorData
@using Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.Player
@using Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.IncidentDetails
@using Teslametrics.App.Web.Features.Main.Incidents.IncidentDialog.Preview
@attribute [StreamRendering(true)]
@inherits InteractiveBaseComponent
<MudDialog Visible="_isVisible"
           Class="incidend_dialog"
           ActionsClass="align-center justify-center"
           ContentClass="content"
           Options="_dialogOptions">
    <DialogContent>
        <div class="title d-flex">
            <MudStack Spacing="2">
                <MudText Typo="Typo.subtitle1">Происшествие</MudText>
                @if (IsLoading)
                {
                    <MudSkeleton Width="30%"
                                 Height="42px" />
                }
                @if (!IsLoading && _response is null)
                {
                    <MudText Typo="Typo.body2">Происшествие не найдено</MudText>
                }
                @if (!IsLoading && _response is not null && _response.Incident is not null && _response.Incident.IncidentType is not null)
                {
                    <MudText Typo="Typo.body2">@_response.Incident.IncidentType.Value.GetName()</MudText>
                }
            </MudStack>
            <MudSpacer />
            <div>
                <MudIconButton OnClick="CancelAsync"
                               Class="mr-n3"
                               Icon="@Icons.Material.Outlined.Close" />
            </div>
        </div>
        @if (!IsLoading && _response is not null && _response.Incident is not null)
        {
            <div class="content_grid pb-2">
                <MudStack Spacing="3"
                          Row="_response.Incident.IncidentType == Teslametrics.Shared.IncidentType.WirenboardDisconnected">
                    <div class="mud-width-full d-flex flex-column gap-3">
                        <MudText Typo="Typo.caption"
                                 Class="caption">Общая информация</MudText>
                        <MudPaper Elevation="0"
                                  Outlined="false"
                                  Class="py-4 px-3 information d-flex gap-2 flex-column">
                            <MudText>Оборудование: @(_response.Fridge?.Name ?? (_response.Incident.SensorId == Guid.Empty ? "Wirenboard" : "Неизвестно"))</MudText>
                            <MudText>Тип происшествия: @_response!.Incident.IncidentType!.Value.GetName()</MudText>
                            <MudText>Дата: @FormatGmt3(_response.Incident.FiredAt).ToString("dd MMMM yyyy")</MudText>
                            <MudText>Время: @FormatGmt3(_response.Incident.FiredAt).ToString("HH:mm:ss")</MudText>
                        </MudPaper>
                    </div>
                    <div class="mud-width-full d-flex flex-column gap-3">
                        <MudText Typo="Typo.caption"
                                 Class="caption">На момент происшествия</MudText>
                        <MudPaper Elevation="0"
                                  Class="@(_response.Incident.ResolvedAt is null ? "item d-flex gap-2 error_description" : "item d-flex gap-2 error_description error_description--resolved")">
                            <MudIcon Icon="@TeslaIcons.State.Warning"
                                     Color="@Color.Error"
                                     Size="@Size.Small" />
                            <div class="d-flex gap-2 flex-column">
                                @switch (_sensorModel)
                                {
                                    case GetIncidentUseCase.Response.DoorModel door:
                                        <DoorIncidentData FiredAt="@_response.Incident.FiredAt"
                                                          ResolvedAd="@_response.Incident.ResolvedAt" />
                                        break;
                                    case GetIncidentUseCase.Response.TemperatureModel temperature:
                                        <TempIncidentData MaxTemp="@temperature.MaxTemp"
                                                          MinTemp="@temperature.MinTemp"
                                                          FiredAt="@_response.Incident.FiredAt"
                                                          ResolvedAd="@_response.Incident.ResolvedAt" />
                                        break;
                                    case GetIncidentUseCase.Response.HumidityModel humidity:
                                        <HumidityIncidentData MinHumidity="@humidity.MinHumidity"
                                                              MaxHumidity="@humidity.MaxHumidity"
                                                              FiredAt="@_response.Incident.FiredAt"
                                                              ResolvedAd="@_response.Incident.ResolvedAt" />
                                        break;
                                    case GetIncidentUseCase.Response.LeakModel leak:
                                        <LeakIncidentData FiredAt="@_response.Incident.FiredAt"
                                                          ResolvedAd="@_response.Incident.ResolvedAt" />
                                        break;
                                    case GetIncidentUseCase.Response.PowerModel power:
                                        <PowerIncidentData FiredAt="@_response.Incident.FiredAt"
                                                           ResolvedAd="@_response.Incident.ResolvedAt" />
                                        break;
                                    case null:
                                        if (_response.Incident.IncidentType == Teslametrics.Shared.IncidentType.WirenboardDisconnected)
                                        {
                                            <WirenboardDisconnectedIncidentData FiredAt="@_response.Incident.FiredAt"
                                                                                ResolvedAd="@_response.Incident.ResolvedAt" />
                                        }
                                        else
                                        {
                                            <UnknownSensorData />
                                        }
                                        break;
                                    default:
                                        <UnknownSensorData />
                                        break;
                                }
                            </div>
                        </MudPaper>
                    </div>
                </MudStack>
                @if (_response.Fridge is not null && _response.Fridge.Sensors.Any())
                {
                    <MudStack Spacing="3">
                        <MudText Typo="Typo.caption"
                                 Class="caption">Текущие показатели оборудования</MudText>
                        <div class="tile_grid">
                            @foreach (var item in _temperature ?? [])
                            {
                                <MudPaper Elevation="0"
                                          Outlined="true"
                                          Class="pa-4 tile"
                                          @key="item.Id">
                                    <SensorDataComponent TopicName="@item.Name"
                                                         Name="@item.DisplayName"
                                                         ErrorFunc="@(new Func<object?, bool>(value => TemperatureErrorFunc(item, value)))"
                                                         ValueProcessor="TempValueProcessor"
                                                         Icon="@TeslaIcons.Sensors.Temperature" />
                                </MudPaper>
                            }
                            @foreach (var item in _humidity ?? [])
                            {
                                <MudPaper Elevation="0"
                                          Outlined="true"
                                          Class="pa-4 tile"
                                          @key="item.Id">
                                    <SensorDataComponent TopicName="@item.Name"
                                                         Name="@item.DisplayName"
                                                         ErrorFunc="@(new Func<object?, bool>(value => HumidityErrorFunc(item, value)))"
                                                         ValueProcessor="HumidityValueProcessor"
                                                         Icon="@TeslaIcons.Sensors.Humidity" />
                                </MudPaper>
                            }
                            @foreach (var item in _door ?? [])
                            {
                                <MudPaper Elevation="0"
                                          Outlined="true"
                                          Class="pa-4 tile"
                                          @key="item.Id">
                                    <SensorDataComponent TopicName="@item.Name"
                                                         Name="@item.DisplayName"
                                                         ErrorFunc="DoorErrorFunc"
                                                         ValueProcessor="DoorValueProcessor"
                                                         Icon="@TeslaIcons.Sensors.Door" />
                                </MudPaper>
                            }
                            @foreach (var item in _power ?? [])
                            {
                                <MudPaper Elevation="0"
                                          Outlined="true"
                                          Class="pa-4 tile"
                                          @key="item.Id">
                                    <SensorDataComponent TopicName="@item.Name"
                                                         Name="@item.DisplayName"
                                                         ErrorFunc="PowerErrorFunc"
                                                         ValueProcessor="PowerValueProcessor"
                                                         Icon="@TeslaIcons.Sensors.Power" />
                                </MudPaper>
                            }
                            @foreach (var item in _leak ?? [])
                            {
                                <MudPaper Elevation="0"
                                          Outlined="true"
                                          Class="pa-4 tile"
                                          @key="item.Id">
                                    <SensorDataComponent TopicName="@item.Name"
                                                         Name="@item.DisplayName"
                                                         ErrorFunc="LeakErrorFunc"
                                                         ValueProcessor="LeakValueProcessor"
                                                         Icon="@TeslaIcons.Sensors.Door" />
                                </MudPaper>
                            }
                        </div>
                    </MudStack>
                }
                @if (_response.Cameras.Any())
                {
                    <MudStack Spacing="3"
                              Class="player">
                        <MudText Typo="Typo.caption"
                                 Class="caption">Данные с камер</MudText>
                        @foreach (var camera in _response.Cameras)
                        {
                            <CameraPreview CameraId="@camera.Id"
                                           CameraStreamId="@camera.CameraStreamId"
                                           FiredAt="@_firedAt"
                                           OrganizationId="@camera.OrganizationId"
                                           @key="camera.Id" />
                        }
                    </MudStack>
                }
            </div>
        }
    </DialogContent>
</MudDialog>

<style>
    .incidend_dialog.mud-dialog-width-md {
        max-width: 1030px;
    }

    .incidend_dialog.mud-dialog .mud-dialog-content {
        padding: 0px 20px;
    }
</style>
